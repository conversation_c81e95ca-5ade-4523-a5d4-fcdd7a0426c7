const { getUnpaidOrders, getOrderPaymentDetails } = require('../controllers/unpaid_orders.controller');
const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");

const router = Router();


router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.REPORTS]),
  getUnpaidOrders
);

router.get(
    "/:orderId/payment-details",
    isLoggedIn,
    isAuthenticated,
    isSubscriptionActive,
    authorize([SCOPES.REPORTS]),
    getOrderPaymentDetails
  );


module.exports = router;
