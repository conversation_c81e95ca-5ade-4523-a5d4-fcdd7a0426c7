const {
    addRecipeDB,
    updateRecipeDB,
    getRecipeDetailsDB,
    checkRecipeStockDB,
    processRecipeStockDB,
    getAllRecipesDB,
    deleteRecipeDB,
} = require("../services/recipe.service");
const { getMenuItems } = require("../services/menu_item.service");
const { getAllIngredients } = require("../services/ingredient.service");

exports.getAllRecipes = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const recipes = await getAllRecipesDB(tenantId);

        return res.status(200).json(recipes);
    } catch (error) {
        console.error('Reçete listesi hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Reçeteler listelenirken bir hata oluştu"
        });
    }
};

exports.getRecipeInitialData = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        
        // Ürünleri ve hammaddeleri paralel olarak getir
        const [menuItems, ingredients] = await Promise.all([
            getMenuItems(tenantId),
            getAllIngredients(tenantId)
        ]);

        return res.status(200).json({
            success: true,
            data: {
                menuItems,
                ingredients
            }
        });
    } catch (error) {
        console.error('Veri getirme hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Veriler alınırken bir hata oluştu"
        });
    }
};

exports.deleteRecipe = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { id } = req.params;

        await deleteRecipeDB(id, tenantId);

        return res.status(200).json({
            success: true,
            message: "Reçete başarıyla silindi"
        });
    } catch (error) {
        console.error('Reçete silme hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Reçete silinirken bir hata oluştu"
        });
    }
};

exports.addRecipe = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const userId = req.user.id;
        const name = req.user.tenant_id;

        const {
            menuItemId,
            ingredients,
        } = req.body;

        // Validasyon
        if (!menuItemId || !ingredients?.length) {
            return res.status(400).json({
                success: false,
                message: "Ürün ve en az bir hammadde gereklidir"
            });
        }

        const result = await addRecipeDB({
            menuItemId,
            ingredients,
            tenantId,
            name,
            userId
        });

        return res.status(200).json({
            success: true,
            message: "Reçete başarıyla oluşturuldu",
            data: result
        });
    } catch (error) {
        console.error('Reçete oluşturma hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Reçete oluşturulurken bir hata oluştu"
        });
    }
};

exports.updateRecipe = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { id } = req.params;
        const {
            name,
            portionSize,
            portionUnit,
            preparationTime,
            cookingTime,
            instructions,
            ingredients
        } = req.body;

        const newCost = await updateRecipeDB(id, {
            name,
            portionSize,
            portionUnit,
            preparationTime,
            cookingTime,
            instructions,
            ingredients,
            tenantId
        });

        return res.status(200).json({
            success: true,
            message: "Reçete başarıyla güncellendi",
            data: { newCost }
        });
    } catch (error) {
        console.error('Reçete güncelleme hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Reçete güncellenirken bir hata oluştu"
        });
    }
};

exports.getRecipeDetails = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { menuItemId } = req.params; // Ürün ID'sine göre reçete getir

        const recipeDetails = await getRecipeDetailsDB(menuItemId, tenantId);

        return res.status(200).json({
            success: true,
            data: recipeDetails
        });
    } catch (error) {
        console.error('Reçete detay hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Reçete detayları alınırken bir hata oluştu"
        });
    }
};

exports.checkRecipeStock = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { recipeId, quantity } = req.query;
        
        const stockStatus = await checkRecipeStockDB(recipeId, quantity, tenantId);
        
        // Her malzemenin stok durumunu kontrol et
        const isAvailable = stockStatus.every(item => item.isAvailable);
        
        return res.status(200).json({
            success: true,
            data: {
                stockStatus,
                isAvailable,
                shortages: stockStatus.filter(item => !item.isAvailable)
            }
        });
    } catch (error) {
        console.error('Stok kontrol hatası:', error);
        return res.status(500).json({
            success: false,
            message: "Stok kontrolü sırasında bir hata oluştu"
        });
    }
};

// Ayrıca reçeteye göre stok düşme işlemi için yeni bir kontrolör ekleyelim
exports.processRecipeStock = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const userId = req.user.id;
        const { recipeId, quantity, referenceType, referenceId } = req.body;

        await processRecipeStockDB(
            recipeId, 
            quantity, 
            referenceType,
            referenceId,
            tenantId,
            userId
        );

        return res.status(200).json({
            success: true,
            message: "Reçete stok işlemi başarıyla tamamlandı"
        });
    } catch (error) {
        console.error('Stok işleme hatası:', error);
        return res.status(500).json({
            success: false,
            message: error.message || "Stok işlemi sırasında bir hata oluştu"
        });
    }
};