const { getKitchenOrdersDB, updateOrderItemStatusDB } = require("../services/kitchen.service");

exports.getKitchenOrders = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const {addons,kitchenOrders,kitchenOrdersItems} = await getKitchenOrdersDB(tenantId);

    const formattedOrders = kitchenOrders.map((order)=>{
      const orderItems = kitchenOrdersItems.filter((oi)=>oi.order_id == order.id);

      orderItems.forEach((oi, index)=>{
        const addonsIds = oi?.addons ? JSON.parse(oi?.addons) : null;

        if(addonsIds) {
          const itemAddons = addonsIds.map((addonId)=>{
            const addon = addons.filter((a)=>a.id == addonId);
            return addon[0];
          });
          orderItems[index].addons = [...itemAddons];
        }
      });

      return {
        ...order,
        items: orderItems
      }
    })

    return res.status(200).json(formattedOrders);
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateKitchenOrderItemStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const orderItemId = req.params.id;
    const { status, reasonId } = req.body

    if(!status) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!"
      });
    }

    // İptal, ikram veya zayi işlemlerinde sebep kontrolü
    if ((status === 'cancelled' || status === 'complimentary' || status === 'waste') && !reasonId) {
      return res.status(400).json({
        success: false,
        message: status === 'cancelled' ? "İptal nedeni seçilmelidir!" :
                 status === 'complimentary' ? "İkram nedeni seçilmelidir!" :
                 "Fire nedeni seçilmelidir!"
      });
    }

    await updateOrderItemStatusDB(orderItemId, status, tenantId, username, reasonId)

    return res.status(200).json({
      success: true,
      message: "Siparişteki Ürün Durumu Değişti"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};