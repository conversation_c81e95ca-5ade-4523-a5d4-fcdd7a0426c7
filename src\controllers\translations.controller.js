const translationService = require('../services/translations.service');
const { Translate } = require('@google-cloud/translate').v2;

const translate = new Translate({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  key: process.env.GOOGLE_CLOUD_TRANSLATE_API_KEY
});


// Tüm çevirileri getir
exports.getTranslations = async (req, res) => {
  try {
    const tenant_id = req.user.tenant_id;
    const translations = await translationService.getTranslationsDB(tenant_id);
    
    return res.status(200).json({
      success: true,
      translations,
    });
  } catch (error) {
    console.error("getTranslations hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata olu<PERSON>, lütfen daha sonra tekrar deneyin.",
    });
  }
};

// Tek bir çeviri ekle veya güncelle
exports.addOrUpdateTranslation = async (req, res) => {
  try {
    const tenant_id = req.user.tenant_id;
    const { object_type, object_id, language_code, translation } = req.body;
    
    // Gerekli alanları kontrol et
    if (!(tenant_id && object_type && object_id && language_code && translation)) {
      return res.status(400).json({
        success: false,
        message: "Gerekli alanlar eksik.",
      });
    }
    
    const id = await translationService.addOrUpdateTranslationDB(
      tenant_id,
      object_type,
      object_id,
      language_code,
      translation
    );
    
    return res.status(200).json({
      success: true,
      message: "Çeviri başarıyla kaydedildi.",
      id,
    });
  } catch (error) {
    console.error("addOrUpdateTranslation hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin.",
    });
  }
};

// Çoklu çeviri ekle veya güncelle
exports.addOrUpdateMultipleTranslations = async (req, res) => {
  try {
    const tenant_id = req.user.tenant_id;
    const { object_type, object_id, translations } = req.body;
    
    // Gerekli alanları kontrol et
    if (!(tenant_id && object_type && object_id && translations && typeof translations === 'object')) {
      return res.status(400).json({
        success: false,
        message: "Gerekli alanlar eksik veya hatalı.",
      });
    }
    
    // En az bir dil için çeviri olmalı
    const hasTranslations = Object.values(translations).some(val => val && val.trim() !== '');
    if (!hasTranslations) {
      return res.status(400).json({
        success: false,
        message: "En az bir dil için çeviri eklemelisiniz.",
      });
    }
    
    const results = await translationService.addOrUpdateMultipleTranslationsDB(
      tenant_id,
      object_type,
      object_id,
      translations
    );
    
    return res.status(200).json({
      success: true,
      message: "Çeviriler başarıyla kaydedildi.",
      results,
    });
  } catch (error) {
    console.error("addOrUpdateMultipleTranslations hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin.",
    });
  }
};

// Çeviri sil (ID ile)
exports.deleteTranslation = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Çeviri ID eksik.",
      });
    }
    
    await translationService.deleteTranslationDB(id);
    
    return res.status(200).json({
      success: true,
      message: "Çeviri başarıyla silindi.",
    });
  } catch (error) {
    console.error("deleteTranslation hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin.",
    });
  }
};

// Belirli bir nesne için tüm çevirileri sil
exports.deleteAllTranslationsForObject = async (req, res) => {
  try {
    const tenant_id = req.user.tenant_id;
    const { object_type, object_id } = req.body;
    
    if (!(tenant_id && object_type && object_id)) {
      return res.status(400).json({
        success: false,
        message: "Gerekli alanlar eksik.",
      });
    }
    
    const affectedRows = await translationService.deleteAllTranslationsForObjectDB(
      tenant_id,
      object_type,
      object_id
    );
    
    return res.status(200).json({
      success: true,
      message: `${affectedRows} çeviri başarıyla silindi.`,
      affected_rows: affectedRows
    });
  } catch (error) {
    console.error("deleteAllTranslationsForObject hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin.",
    });
  } 
};

exports.autoTranslate = async (req, res) => {
  try {
    const { items } = req.body;

    // Gelen verileri doğrula
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Çeviri için öğe bulunamadı.",
      });
    }

    // Çeviri sonuçlarını tutacak dizi
    const BATCH_SIZE = 50;
    const translations = [];

    // Her bir batch için döngü
    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batch = items.slice(i, i + BATCH_SIZE);
      
      // Batch için çeviri işlemleri
      const batchTranslations = await Promise.all(
        batch.map(async (item) => {
          const { objectType, objectId, originalText, targetLanguages } = item;

          // Gerekli alanları kontrol et
          if (!(objectType && objectId && originalText && targetLanguages)) {
            return null;
          }

          // Her hedef dil için çeviri yap
          const itemTranslations = {};
          for (const targetLang of targetLanguages) {
            try {
              // Google Translate API'sini kullan
              const [translation] = await translate.translate(originalText, {
                from: 'tr', // Kaynak dil
                to: targetLang // Hedef dil
              });

              // Çeviriyi sonuçlara ekle
              itemTranslations[targetLang] = translation;
            } catch (translateError) {
              console.error(`${targetLang} diline çeviri hatası:`, translateError);
              // Hata durumunda orijinal metni kullan
              itemTranslations[targetLang] = originalText;
            }
          }

          return {
            objectType,
            objectId,
            translations: itemTranslations
          };
        })
      );

      // Null olmayan çevirileri topla
      const validBatchTranslations = batchTranslations.filter(translation => translation !== null);
      translations.push(...validBatchTranslations);

      // API çağrıları arasında kısa bir mola
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Başarılı çeviri sonuçlarını döndür
    return res.status(200).json({
      success: true,
      message: `Toplam ${translations.length} öğe için çeviri tamamlandı.`,
      translations,
    });
  } catch (error) {
    console.error("autoTranslate hata:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin.",
    });
  }
};