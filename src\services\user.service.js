const { getMySqlPromiseConnection } = require("../config/mysql.db")

exports.getUserDB = async (username, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT username, role, scope, is_active FROM users
        WHERE username = ? AND tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [username, tenantId]);
        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllUsersDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT username, name, role, photo, designation, pin, phone, email, scope, is_active FROM users
        WHERE tenant_id = ?
        ORDER BY role, name;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.doUserExistDB = async (username) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT username FROM users
        WHERE username = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [username]);
        return result.length == 1;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addUserDB = async (tenantId, username, encryptedPassword, name, role, photo, designation, phone, email, scope, isActive = true) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO users
        (username, password, name, role, photo, designation, phone, email, scope, is_active, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await conn.query(sql, [username, encryptedPassword, name, role, photo, designation, phone, email, scope, isActive ? 1 : 0, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteUserDB = async (username, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM refresh_tokens WHERE username = ? AND tenant_id = ?;
        DELETE FROM users WHERE username = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [username, tenantId, username, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteUserRefreshTokensDB = async (username, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM refresh_tokens WHERE username = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [username, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateUserDB = async (username, name, photo, pin, phone, email, scope, tenantId, isActive = null) => {
    const conn = await getMySqlPromiseConnection();
    try {
        let sql;
        let params;

        if (isActive !== null) {
            sql = `
            UPDATE users
            SET
            name = ?, photo = ?, pin = ?, phone = ?, email = ?, scope = ?, is_active = ?
            WHERE username = ? AND tenant_id = ?;
            `;
            params = [name, photo, pin, phone, email, scope, isActive ? 1 : 0, username, tenantId];
        } else {
            sql = `
            UPDATE users
            SET
            name = ?, photo = ?, pin = ?, phone = ?, email = ?, scope = ?
            WHERE username = ? AND tenant_id = ?;
            `;
            params = [name, photo, pin, phone, email, scope, username, tenantId];
        }

        await conn.query(sql, params);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateUserPasswordDB = async (username, password, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE users
        SET
        password = ?
        WHERE username = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [password, username, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

// User Floor Restrictions Functions

/**
 * Get all floor restrictions for a user
 * @param {string} username - Username
 * @param {number} tenantId - Tenant ID
 * @returns {Array} - Array of floor IDs that the user is restricted from
 */
exports.getUserFloorRestrictionsDB = async (username, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        SELECT floor_id FROM user_floor_restrictions
        WHERE username = ? AND tenant_id = ?
        `;

        const [result] = await conn.query(sql, [username, tenantId]);
        return result.map(row => row.floor_id);
    } catch (error) {
        console.error("Error in getUserFloorRestrictionsDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Add a floor restriction for a user
 * @param {string} username - Username
 * @param {number} floorId - Floor ID to restrict
 * @param {number} tenantId - Tenant ID
 * @returns {boolean} - Success status
 */
exports.addUserFloorRestrictionDB = async (username, floorId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        INSERT INTO user_floor_restrictions (username, floor_id, tenant_id)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE updated_at = NOW()
        `;

        await conn.query(sql, [username, floorId, tenantId]);
        return true;
    } catch (error) {
        console.error("Error in addUserFloorRestrictionDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Remove a floor restriction for a user
 * @param {string} username - Username
 * @param {number} floorId - Floor ID to remove restriction
 * @param {number} tenantId - Tenant ID
 * @returns {boolean} - Success status
 */
exports.removeUserFloorRestrictionDB = async (username, floorId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        DELETE FROM user_floor_restrictions
        WHERE username = ? AND floor_id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [username, floorId, tenantId]);
        return true;
    } catch (error) {
        console.error("Error in removeUserFloorRestrictionDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Update all floor restrictions for a user
 * @param {string} username - Username
 * @param {Array} floorIds - Array of floor IDs to restrict
 * @param {number} tenantId - Tenant ID
 * @returns {boolean} - Success status
 */
exports.updateUserFloorRestrictionsDB = async (username, floorIds, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        await conn.beginTransaction();

        // First, remove all existing restrictions
        const deleteSQL = `
        DELETE FROM user_floor_restrictions
        WHERE username = ? AND tenant_id = ?
        `;
        await conn.query(deleteSQL, [username, tenantId]);

        // Then, add new restrictions if any
        if (floorIds && floorIds.length > 0) {
            const insertValues = floorIds.map(floorId => [username, floorId, tenantId]);
            const insertSQL = `
            INSERT INTO user_floor_restrictions (username, floor_id, tenant_id)
            VALUES ?
            `;
            await conn.query(insertSQL, [insertValues]);
        }

        await conn.commit();
        return true;
    } catch (error) {
        await conn.rollback();
        console.error("Error in updateUserFloorRestrictionsDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};