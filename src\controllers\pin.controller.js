const { setPinDB, verifyPinDB } = require("../services/pin.service");
const { generateAccessToken, generateRefreshToken } = require("../utils/jwt");
const { CONFIG } = require("../config");
const { addRefreshTokenDB, getTenantNameDB } = require("../services/auth.service");

exports.setPin = async (req, res) => {
  try {
    const { pin } = req.body;
    const userId = req.user.id;
    const tenantId = req.user.tenant_id;

    if (!pin || pin.length !== 6) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir 6 haneli PIN giriniz!"
      });
    }

    await setPinDB(userId, pin, tenantId);

    return res.status(200).json({
      success: true,
      message: "PIN başarıyla kaydedildi"
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "PIN kaydedilirken bir hata oluştu!"
    });
  }
};

exports.verifyPin = async (req, res) => {
    try {
      const { pin, tenant_id } = req.body;
      const tenantId = tenant_id;

      if (!pin || pin.length !== 6) {
        return res.status(400).json({
          success: false,
          message: "Geçerli bir 6 haneli PIN giriniz!"
        });
      }

      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: "Tenant ID gereklidir!"
        });
      }

      const user = await verifyPinDB(pin, tenantId);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: "Geçersiz PIN!"
        });
      }

      // Token oluşturma işlemleri
      const cookieOptions = {
        expires: new Date(Date.now() + parseInt(CONFIG.COOKIE_EXPIRY)),
        httpOnly: true,
        domain: CONFIG.FRONTEND_DOMAIN_COOKIE,
        sameSite: false,
        secure: process.env.NODE_ENV == "production",
        path: "/"
      };

      const refreshTokenExpiry = new Date(Date.now() + parseInt(CONFIG.COOKIE_EXPIRY_REFRESH));
      const cookieRefreshTokenOptions = {
        expires: refreshTokenExpiry,
        httpOnly: true,
        domain: CONFIG.FRONTEND_DOMAIN_COOKIE,
        sameSite: false,
        secure: process.env.NODE_ENV == "production",
        path: "/"
      };

      const payload = {
        tenant_id: user.tenant_id,
        username: user.username,
        name: user.name,
        role: user.role,
        scope: user.scope,
        is_active: user.is_active
      };

      const accessToken = generateAccessToken(payload);
      const refreshToken = generateRefreshToken(payload);

      res.cookie('accessToken', accessToken, cookieOptions);
      res.cookie('refreshToken', refreshToken, cookieRefreshTokenOptions);
      res.cookie('restroprosaas__authenticated', true, {
        expires: new Date(Date.now() + parseInt(CONFIG.COOKIE_EXPIRY_REFRESH)),
        domain: CONFIG.FRONTEND_DOMAIN_COOKIE,
        sameSite: false,
        secure: process.env.NODE_ENV == "production",
        path: "/"
      });

      // Refresh token'ı veritabanına kaydet
      const deviceDetails = req.useragent;
      const deviceIP = req.connection.remoteAddress;
      const deviceName = `${deviceDetails.platform}\nBrowser: ${deviceDetails.browser}`;
      const deviceLocation = "";

      await addRefreshTokenDB(user.username, refreshToken, refreshTokenExpiry, deviceIP, deviceName, deviceLocation, user.tenant_id);

      // Mağaza adını al
      const storeName = await getTenantNameDB(user.tenant_id);

      // Kullanıcının KASIYER yetkisine sahip olup olmadığını kontrol et
      const hasKasiyerScope = user.scope && user.scope.includes('KASIYER');

      // Mobil cihaz kontrolü
      const isMobile = req.useragent.isMobile || req.useragent.isTablet;

      // Kullanıcı bilgilerini ve token'ları döndür
      return res.status(200).json({
        success: true,
        message: "PIN doğrulandı",
        accessToken,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          username: user.username,
          role: user.role,
          tenant_id: user.tenant_id,
          scope: user.scope,
          store_name: storeName,
          disableScreenLock: hasKasiyerScope || isMobile // KASIYER yetkisi varsa veya mobil cihazsa ekran kilidini devre dışı bırak
          // Hassas bilgileri göndermiyoruz (password, pin vb.)
        }
      });

    } catch (error) {
      console.error(error);
      return res.status(500).json({
        success: false,
        message: "PIN doğrulanırken bir hata oluştu!"
      });
    }
  };