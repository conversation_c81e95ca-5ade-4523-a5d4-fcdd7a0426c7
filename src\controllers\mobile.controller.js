// controllers/mobile.controller.js
const {
    getAllTenantsDB,
    getMenuItemsDB,
} = require("../services/mobile.service");

const {
    getCategoriesDB,
    getPaymentTypesDB,
    getPrintSettingDB,
    getStoreSettingDB,
    getStoreTablesDB,
  } = require("../services/settings.service");
  const {
    getAllMenuItemsDB,
    getAllAddonsDB,
    getAllVariantsDB,
  } = require("../services/menu_item.service");

exports.getTenantInfo = async (req, res) => {
    try {
    const { tenantId } = req.params;

      const [categories, paymentTypes, printSettings, storeSettings, storeTables] = await Promise.all([
        getCategoriesDB(tenantId),
        getPaymentTypesDB(true, tenantId, true), // forPOS=true to filter out hideOnPos=1
        getPrintSettingDB(tenantId),
        getStoreSettingDB(tenantId),
        getStoreTablesDB(tenantId)
      ]);

      const [menuItems, addons, variants] = await Promise.all([
        getAllMenuItemsDB(tenantId),
        getAllAddonsDB(tenantId),
        getAllVariantsDB(tenantId),
      ]);

      const formattedMenuItems = menuItems.map((item) => {
        const itemAddons = addons.filter((addon) => addon.item_id == item.id);
        const itemVariants = variants.filter(
          (variant) => variant.item_id == item.id
        );

        return {
          ...item,
          addons: [...itemAddons],
          variants: [...itemVariants],
        };
      });

      return res.status(200).json({
        categories,
        paymentTypes,
        printSettings,
        storeSettings,
        storeTables,
        menuItems: formattedMenuItems,
      });
    } catch (error) {
      console.error(error);
      return res.status(500).json({
        success: false,
        message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
      });
    }
  };

exports.getAllTenants = async (req, res) => {
    try {
        const tenants = await getAllTenantsDB();
        return res.status(200).json({
            success: true,
            data: tenants
        });
    } catch (error) {
        console.error('Error in getAllTenants:', error);
        return res.status(500).json({
            success: false,
            message: "Bir hata oluştu"
        });
    }
};

exports.getMenuItems = async (req, res) => {
    try {
        const { tenantId } = req;
        const menuItems = await getMenuItemsDB(tenantId);
        return res.status(200).json({
            success: true,
            data: menuItems
        });
    } catch (error) {
        console.error('Error in getMenuItems:', error);
        return res.status(500).json({
            success: false,
            message: "Bir hata oluştu"
        });
    }
};

// Diğer controller fonksiyonları benzer şekilde...