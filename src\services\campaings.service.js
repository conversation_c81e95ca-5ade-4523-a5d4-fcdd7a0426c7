const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.addCampaignDB = async (campaign_name, description, start_date, end_date, tenant_id, image_url) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO campaigns 
      (campaign_name, description, start_date, end_date, tenant_id, image_url)
      VALUES (?, ?, ?, ?, ?, ?);
    `;
    const [result] = await conn.query(sql, [campaign_name, description, start_date, end_date, tenant_id, image_url]);
    return result.insertId;
  } catch (error) {
    console.error("Kampanya eklenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.deleteCampaignDB = async (campaignId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      DELETE FROM campaigns 
      WHERE id = ? AND tenant_id = ?;
    `;
    await conn.query(sql, [campaignId, tenantId]);
  } catch (error) {
    console.error("Kampanya silinirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.listCampaignsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT * FROM campaigns 
      WHERE tenant_id = ?;
    `;
    const [rows] = await conn.query(sql, [tenantId]);
    return rows;
  } catch (error) {
    console.error("Kampanyalar listelenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateCampaignDB = async (campaignId, campaign_name, description, start_date, end_date, tenant_id, image_url) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE campaigns 
      SET campaign_name = ?, description = ?, start_date = ?, end_date = ?, image_url = COALESCE(?, image_url)
      WHERE id = ? AND tenant_id = ?;
    `;
    await conn.query(sql, [campaign_name, description, start_date, end_date, image_url, campaignId, tenant_id]);
  } catch (error) {
    console.error("Kampanya güncellenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateCampaignImageDB = async (campaignId, image_url, tenant_id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE campaigns 
      SET image_url = ?
      WHERE id = ? AND tenant_id = ?;
    `;
    await conn.query(sql, [image_url, campaignId, tenant_id]);
  } catch (error) {
    console.error("Kampanya resmi güncellenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};