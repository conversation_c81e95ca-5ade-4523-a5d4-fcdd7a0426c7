const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  addCampaign,
  deleteCampaign,
  listCampaigns,
  updateCampaign,
  uploadCampaignPhoto,
  removeCampaignPhoto,
} = require("../controllers/campaings.controller");

const router = Router();

router.post(
  "/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.MANAGE_RESERVATIONS]),
  addCampaign
);

router.delete(
  "/delete/:campaignId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.MANAGE_RESERVATIONS]),
  deleteCampaign
);

router.post(
  "/update/:campaignId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.MANAGE_RESERVATIONS]),
  updateCampaign
);

router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.VIEW_RESERVATIONS]),
  listCampaigns
);

router.post(
  "/upload-photo/:campaignId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.MANAGE_RESERVATIONS]),
  uploadCampaignPhoto
);

router.delete(
  "/remove-photo/:campaignId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.MANAGE_RESERVATIONS]),
  removeCampaignPhoto
);

module.exports = router;