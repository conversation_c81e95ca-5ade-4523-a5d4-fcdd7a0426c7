const { getMySqlPromiseConnection } = require("../config/mysql.db");

class DeliveryPlatformService {
    constructor() {
        this.platforms = {
            GETIR: 1,
            YEMEKSEPETI: 2,
            TRENDYOL: 3
        };
        
        this.orderStatus = {
            PENDING: 'pending',
            CONFIRMED: 'confirmed',
            PREPARING: 'preparing',
            READY: 'ready',
            PICKED_UP: 'picked_up',
            DELIVERED: 'delivered',
            CANCELLED: 'cancelled'
        };
    }

    async getPlatformCredentials(tenantId, platformId) {
        const conn = await getMySqlPromiseConnection();
        try {
            const [credentials] = await conn.query(
                'SELECT * FROM tenant_delivery_platforms WHERE tenant_id = ? AND platform_id = ?',
                [tenantId, platformId]
            );
            return credentials[0];
        } finally {
            conn.release();
        }
    }

    async savePlatformOrder(orderData) {
        const conn = await getMySqlPromiseConnection();
        try {
            await conn.beginTransaction();

            // Ana sipariş kaydı
            const [orderResult] = await conn.query(
                `INSERT INTO platform_orders 
                (tenant_id, platform_id, platform_order_id, order_status, customer_name, 
                customer_phone, customer_address, total_amount, platform_commission, 
                payment_status, payment_type, order_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    orderData.tenantId,
                    orderData.platformId,
                    orderData.platformOrderId,
                    orderData.status,
                    orderData.customerName,
                    orderData.customerPhone,
                    orderData.customerAddress,
                    orderData.totalAmount,
                    orderData.commission,
                    orderData.paymentStatus,
                    orderData.paymentType,
                    orderData.orderType
                ]
            );

            // Sipariş detaylarını kaydet
            const orderId = orderResult.insertId;
            for (const item of orderData.items) {
                await conn.query(
                    `INSERT INTO platform_order_items 
                    (platform_order_id, menu_item_id, quantity, unit_price, total_price, notes) 
                    VALUES (?, ?, ?, ?, ?, ?)`,
                    [
                        orderId,
                        item.menuItemId,
                        item.quantity,
                        item.unitPrice,
                        item.totalPrice,
                        item.notes
                    ]
                );
            }

            await conn.commit();
            return orderId;
        } catch (error) {
            await conn.rollback();
            throw error;
        } finally {
            conn.release();
        }
    }

    async updateOrderStatus(orderId, tenantId, status) {
        const conn = await getMySqlPromiseConnection();
        try {
            await conn.query(
                'UPDATE platform_orders SET order_status = ? WHERE id = ? AND tenant_id = ?',
                [status, orderId, tenantId]
            );

            const [order] = await conn.query(
                'SELECT platform_id FROM platform_orders WHERE id = ?',
                [orderId]
            );

            if (order[0]) {
                await this.syncOrderStatusWithPlatform(
                    tenantId, 
                    order[0].platform_id, 
                    orderId, 
                    status
                );
            }
        } finally {
            conn.release();
        }
    }

    async getTenantOrders(tenantId, filters = {}) {
        const conn = await getMySqlPromiseConnection();
        try {
            let query = `
                SELECT po.*, dp.name as platform_name 
                FROM platform_orders po
                JOIN delivery_platforms dp ON po.platform_id = dp.id
                WHERE po.tenant_id = ?
            `;

            const queryParams = [tenantId];

            if (filters.status) {
                query += ' AND po.order_status = ?';
                queryParams.push(filters.status);
            }

            if (filters.platformId) {
                query += ' AND po.platform_id = ?';
                queryParams.push(filters.platformId);
            }

            if (filters.dateFrom) {
                query += ' AND po.created_at >= ?';
                queryParams.push(filters.dateFrom);
            }

            if (filters.dateTo) {
                query += ' AND po.created_at <= ?';
                queryParams.push(filters.dateTo);
            }

            query += ' ORDER BY po.created_at DESC';

            const [orders] = await conn.query(query, queryParams);
            return orders;
        } finally {
            conn.release();
        }
    }

    async syncMenuWithPlatform(tenantId, platformId) {
        const conn = await getMySqlPromiseConnection();
        try {
            // Menü öğelerini al
            const [menuItems] = await conn.query(
                `SELECT * FROM menu_items WHERE tenant_id = ? AND is_active = 1`,
                [tenantId]
            );

            // Platform kimlik bilgilerini al
            const credentials = await this.getPlatformCredentials(tenantId, platformId);

            let syncStatus = 'success';
            let syncMessage = 'Menu synced successfully';

            try {
                // Platform-spesifik senkronizasyon mantığı
                switch (platformId) {
                    case this.platforms.GETIR:
                        await this.syncGetirMenu(menuItems, credentials);
                        break;
                    case this.platforms.YEMEKSEPETI:
                        await this.syncYemeksepetiMenu(menuItems, credentials);
                        break;
                    case this.platforms.TRENDYOL:
                        await this.syncTrendyolMenu(menuItems, credentials);
                        break;
                }
            } catch (error) {
                syncStatus = 'error';
                syncMessage = error.message;
                throw error;
            } finally {
                // Senkronizasyon geçmişini kaydet
                await conn.query(
                    `INSERT INTO menu_sync_history (tenant_id, platform_id, sync_status, sync_message)
                    VALUES (?, ?, ?, ?)`,
                    [tenantId, platformId, syncStatus, syncMessage]
                );
            }
        } finally {
            conn.release();
        }
    }
}

module.exports = new DeliveryPlatformService();