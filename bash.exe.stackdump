Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB4D300000 ntdll.dll
7FFB4BB10000 KERNEL32.DLL
7FFB4A570000 KERNELBASE.dll
7FFB4C950000 USER32.dll
7FFB4A9F0000 win32u.dll
7FFB4B9D0000 GDI32.dll
7FFB4AD80000 gdi32full.dll
7FFB4A940000 msvcp_win.dll
7FFB4AA20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB4C4F0000 advapi32.dll
7FFB4C440000 msvcrt.dll
7FFB4CF10000 sechost.dll
7FFB4BBE0000 RPCRT4.dll
7FFB49A80000 CRYPTBASE.DLL
7FFB4AB70000 bcryptPrimitives.dll
7FFB4D160000 IMM32.DLL
