// services/floorPrinterMappings.service.js
const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.addFloorPrinterMappingService = async (tenantId, floorId, categoryId, printerId, printerType) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `
      INSERT INTO floor_printer_mappings
      (floor_id, category_id, printer_id, printer_type, tenant_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, NOW(), NOW())
    `;
    await conn.execute(query, [floorId, categoryId || null, printerId, printerType, tenantId]);
    return true;
  } catch (error) {
    console.error("Floor-printer mapping eklenirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.getFloorPrinterMappingsService = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `
      SELECT id, floor_id, category_id, printer_id, printer_type, tenant_id, created_at, updated_at
      FROM floor_printer_mappings
      WHERE tenant_id = ?
    `;
    const [rows] = await conn.execute(query, [tenantId]);
    return rows;
  } catch (error) {
    console.error("Floor-printer mapping'ler alınırken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.updateFloorPrinterMappingService = async (mappingId, floorId, categoryId, printerId, printerType) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `
      UPDATE floor_printer_mappings
      SET floor_id = ?, category_id = ?, printer_id = ?, printer_type = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await conn.execute(query, [floorId, categoryId || null, printerId, printerType, mappingId]);
    return true;
  } catch (error) {
    console.error("Floor-printer mapping güncellenirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.deleteFloorPrinterMappingService = async (mappingId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `DELETE FROM floor_printer_mappings WHERE id = ?`;
    await conn.execute(query, [mappingId]);
    return true;
  } catch (error) {
    console.error("Floor-printer mapping silinirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};