// src/routes/delivery.routes.js
const { Router } = require('express');
const { isLoggedIn, isAuthenticated } = require('../middlewares/auth.middleware');
const { 
    handleGetirWebhook, 
    handleYemeksepetiWebhook, 
    handleTrendyolWebhook,
    getTenantOrders,
    updateOrderStatus,
    handleGetirNewOrder,
    handleGetirCancelOrder,
    handleGetirCourierArrival,
    handleGetirRestaurantStatus,
} = require('../controllers/delivery.controller');

const router = Router();


// Getir webhook endpoints
router.post('/webhook/getir/new-order', handleGetirNewOrder);
router.post('/webhook/getir/cancel-order', handleGetirCancelOrder);
router.post('/webhook/getir/courier-arrival', handleGetirCourierArrival);
router.post('/webhook/getir/restaurant-status', handleGetirRestaurantStatus);

// Platform webhook endpoints
router.post('/webhook/getir', handleGetirWebhook);
router.post('/webhook/yemeksepeti', handleYemeksepetiWebhook);
router.post('/webhook/trendyol', handleTrendyolWebhook);

// Tenant işlemleri
router.get('/orders', isLoggedIn, isAuthenticated, getTenantOrders);
router.put('/orders/:orderId/status', isLoggedIn, isAuthenticated, updateOrderStatus);

module.exports = router;