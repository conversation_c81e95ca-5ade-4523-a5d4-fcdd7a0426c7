const { getMySqlPromiseConnection } = require("../config/mysql.db")
const { escape } = require("mysql2")
exports.doCustomerExistDB = async (phone, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT phone, name FROM customers
        WHERE phone = ? AND tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [phone, tenantId]);

        return result.length > 0;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addCustomerDB = async (phone, name, email, birthDate, gender, isMember, address, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        INSERT INTO customers
        (phone, name, email, birth_date, gender, is_member, address, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [phone, name, email, birthDate, gender, isMember, address, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getCustomersDB = async (page, perPage, sort, filter, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const currentPage = parseInt(page) || 1;
        const limit = parseInt(perPage) || 10;
        const offset = (currentPage - 1) * limit;
        const sortedBy = sort ? `ORDER BY ${escape(sort)}` : 'ORDER BY created_at DESC';
        const filterQuery = filter ? `WHERE (name LIKE '${filter}%' OR phone='${filter}') AND tenant_id=${tenantId}` : `WHERE tenant_id=${tenantId}`;

        const [customers] = await conn.execute(
            `SELECT phone, name, email, birth_date, gender, is_member, address, balance, allow_credit, created_at FROM customers ${filterQuery} ${sortedBy} LIMIT ${limit} OFFSET ${offset} ;`
        );

        const [totalCustomers] = await conn.execute(
            `SELECT COUNT(*) AS total FROM customers ${filterQuery} ;`
        );

        const response = {
            customers,
            currentPage,
            perPage,
            totalPages: Math.ceil(totalCustomers[0].total / limit),
            totalCustomers: totalCustomers[0].total
        };

        return response;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.getCustomerDB = async (phone, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const [result] = await conn.execute(
            `SELECT phone, name, email, birth_date, gender, is_member, address, created_at FROM customers
            WHERE phone = ? AND tenant_id = ?
            LIMIT 1;`,
            [phone, tenantId]
        );

        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.searchCustomerDB = async (searchString, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const [result] = await conn.execute(
            `
            SELECT phone, name, email, birth_date, gender, is_member, address, created_at FROM customers
            WHERE (phone LIKE ? OR name LIKE ?) AND tenant_id = ?
            LIMIT 10;
            `,
            [`${searchString}%`, `%${searchString}%`, tenantId]
        );

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.updateCustomerDB = async (phone, name, email, birthDate, gender, address, allow_credit, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        UPDATE customers
        SET
        name = ?, email = ?, birth_date = ?, gender = ?, address = ?, allow_credit = ?
        WHERE phone = ? AND tenant_id = ?
        `;

        await conn.query(sql, [name, email, birthDate, gender, address, allow_credit, phone, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.deleteCustomerDB = async (phone, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM customers
        WHERE phone = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [phone, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
