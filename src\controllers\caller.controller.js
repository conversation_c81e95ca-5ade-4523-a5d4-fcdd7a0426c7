const { saveCallDB, getCallsDB } = require("../services/caller.service");

exports.saveCall = async (req, res) => {
    try {
        const tenantId = req.params.tenantId;
        const { phoneNumber, callType, deviceInfo } = req.body;

        // Gerekli alanları kontrol et
        if (!tenantId || !phoneNumber || !callType) {
            return res.status(400).json({
                success: false,
                message: "Eksik bilgi: tenant_id, phone_number ve call_type zorunludur"
            });
        }

        // deviceInfo objesini string'e çevir
        const deviceInfoString = deviceInfo ? JSON.stringify(deviceInfo) : null;

        const callData = {
            tenant_id: tenantId,
            phone_number: phoneNumber,
            call_type: callType,
            device_info: deviceInfoString,
            created_at: new Date()
        };

        console.log("Kaydedilecek çağrı verisi:", callData);

        const result = await saveCallDB(callData);
        
        res.status(200).json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error("Error in saveCall:", error);
        res.status(500).json({
            success: false,
            message: "Çağrı kaydedilemedi"
        });
    }
};

exports.getCalls = async (req, res) => {
    
    try {
        const tenantId = req.user.tenant_id;
        const calls = await getCallsDB(tenantId);
        res.status(200).json({
            success: true,
            data: calls
        });
    } catch (error) {
        console.error("Error in getCalls:", error);
        res.status(500).json({
            success: false,
            message: "Çağrılar alınamadı"
        });
    }
};