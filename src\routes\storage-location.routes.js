const express = require("express");
const router = express.Router();

// Middleware imports
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware")
const { SCOPES } = require("../config/user.config");

// Controller imports
const {
  getStorageLocationSettings,
  updateStorageLocationSettings,
  getStorageLocations,
  getStorageLocationById,
  createStorageLocation,
  updateStorageLocation,
  deleteStorageLocation,
  getStorageLocationFloors,
  createStorageLocationFloorMapping,
  updateStorageLocationFloorMapping,
  deleteStorageLocationFloorMapping,
  getLocationInventory,
  addLocationStockMovement,
  transferStockBetweenLocations,
  getLocationStockMovements,
  getLocationStockSummary
} = require("../controllers/storage-location.controller");

/**
 * Depo/Bar Lokasyonları Modülü Routes
 * Bu modül işletmelerin farklı depo ve barlarını yönetmesini sağlar
 */

// ============================================================================
// MODÜL AYARLARI
// ============================================================================

// Modül ayarlarını getir
router.get(
  "/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getStorageLocationSettings
);

// Modül ayarlarını güncelle
router.put(
  "/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  updateStorageLocationSettings
);

// ============================================================================
// LOKASYON YÖNETİMİ
// ============================================================================

// Tüm lokasyonları getir
router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getStorageLocations
);

// Belirli bir lokasyonu getir
router.get(
  "/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getStorageLocationById
);

// Yeni lokasyon oluştur
router.post(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  createStorageLocation
);

// Lokasyon güncelle
router.put(
  "/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  updateStorageLocation
);

// Lokasyon sil
router.delete(
  "/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  deleteStorageLocation
);

// ============================================================================
// FLOOR-LOKASYON EŞLEŞTİRME
// ============================================================================

// Floor-lokasyon eşleştirmelerini getir
router.get(
  "/floors/mappings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getStorageLocationFloors
);

// Floor-lokasyon eşleştirmesi oluştur
router.post(
  "/floors/mappings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  createStorageLocationFloorMapping
);

// Floor-lokasyon eşleştirmesi güncelle
router.put(
  "/floors/mappings/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  updateStorageLocationFloorMapping
);

// Floor-lokasyon eşleştirmesi sil
router.delete(
  "/floors/mappings/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  deleteStorageLocationFloorMapping
);

// ============================================================================
// LOKASYON BAZLI STOK YÖNETİMİ
// ============================================================================

// Lokasyon bazlı stok miktarlarını getir
router.get(
  "/:id/inventory",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getLocationInventory
);

// Lokasyon bazlı stok hareketi yap
router.post(
  "/:id/movements",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  addLocationStockMovement
);

// Lokasyonlar arası stok transferi
router.post(
  "/transfer",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  transferStockBetweenLocations
);

// Lokasyon bazlı stok hareketlerini getir
router.get(
  "/movements/history",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getLocationStockMovements
);

// Lokasyon bazlı stok özet raporu
router.get(
  "/summary/report",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.INVENTORY_MANAGEMENT]),
  getLocationStockSummary
);

module.exports = router;
