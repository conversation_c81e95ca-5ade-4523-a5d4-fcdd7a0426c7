const { getMySqlPromiseConnection } = require("../config/mysql.db");

// Tedarikçi ekleme
exports.addSupplierDB = async (name, address, phone, email, contactPerson, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO suppliers
      (name, address, phone, email, contact_person, tenant_id)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const [result] = await conn.query(sql, [
      name,
      address || null,
      phone || null,
      email || null,
      contactPerson || null,
      tenantId
    ]);
    return result.insertId;
  } catch (error) {
    console.error("Error in addSupplierDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Tedarikçi güncelleme
exports.updateSupplierDB = async (id, name, address, phone, email, contactPerson, isActive) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE suppliers
      SET name = ?, address = ?, phone = ?, email = ?, contact_person = ?, is_active = ?
      WHERE id = ?
    `;
    const [result] = await conn.query(sql, [
      name,
      address || null,
      phone || null,
      email || null,
      contactPerson || null,
      isActive,
      id
    ]);
    return result.affectedRows;
  } catch (error) {
    console.error("Error in updateSupplierDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Tedarikçi silme
exports.deleteSupplierDB = async (id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `DELETE FROM suppliers WHERE id = ?`;
    const [result] = await conn.query(sql, [id]);
    return result.affectedRows;
  } catch (error) {
    console.error("Error in deleteSupplierDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Tek tedarikçi sorgulama
exports.getSupplierDB = async (id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `SELECT * FROM suppliers WHERE id = ?`;
    const [rows] = await conn.query(sql, [id]);
    return rows[0];
  } catch (error) {
    console.error("Error in getSupplierDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Tüm tedarikçileri sorgulama (tenant bazında)
exports.getAllSuppliersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `SELECT * FROM suppliers WHERE tenant_id = ?`;
    const [rows] = await conn.query(sql, [tenantId]);
    return rows;
  } catch (error) {
    console.error("Error in getAllSuppliersDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};
