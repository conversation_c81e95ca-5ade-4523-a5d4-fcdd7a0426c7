const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * Tarih filtreleme koşulunu oluşturur
 * @param {string} field - Filtrelenecek alan adı
 * @param {string} type - Filtre türü (today, yesterday, last_7days, this_month, last_month, custom)
 * @param {string} from - <PERSON><PERSON><PERSON><PERSON><PERSON> tarihi (custom filtre türü için)
 * @param {string} to - <PERSON><PERSON><PERSON> tarihi (custom filtre türü için)
 * @returns {Object} { params, filter } - Sorgu parametreleri ve filtre koşulu
 */
const getFilterCondition = (field, type, from, to) => {
  const params = [];
  let filter = '';

  switch (type) {
    case 'custom': {
      // Gelen tarih-saat değerlerini UTC+3'ten UTC+0'a dönüştür
      const fromDate = from ? new Date(from) : null;
      const toDate = to ? new Date(to) : null;

      // UTC+0'a dönüştürülmüş tarih-saat değerleri
      if (fromDate && toDate) {
        // MySQL datetime formatına dönüştür: YYYY-MM-DD HH:MM:SS
        const fromISO = fromDate.toISOString().slice(0, 19).replace('T', ' ');
        const toISO = toDate.toISOString().slice(0, 19).replace('T', ' ');

        params.push(fromISO, toISO);
        filter = `${field} >= ? AND ${field} <= ?`;
      }
      break;
    }
    case 'today': {
      // İşletmenin çalışma saatleri: Bugün 08:00'den yarın 04:00'e kadar
      // Bugün 08:00 (sabah 8) UTC+3
      filter = `CONVERT_TZ(${field}, '+00:00', '+03:00') >=
               CONCAT(CURDATE(), ' 08:00:00') AND
               CONVERT_TZ(${field}, '+00:00', '+03:00') <
               CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 04:00:00')`;
      break;
    }
    case 'yesterday': {
      // Dün 08:00'den bugün 04:00'e kadar
      filter = `CONVERT_TZ(${field}, '+00:00', '+03:00') >=
               CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 08:00:00') AND
               CONVERT_TZ(${field}, '+00:00', '+03:00') <
               CONCAT(CURDATE(), ' 04:00:00')`;
      break;
    }
    case 'last_7days': {
      // Son 7 günün her bir günü için 08:00-04:00 aralığını kapsayacak şekilde
      filter = `
          (
              (
                  DATE(CONVERT_TZ(${field}, '+00:00', '+03:00')) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND
                  DATE(CONVERT_TZ(${field}, '+00:00', '+03:00')) < CURDATE() AND
                  HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) >= 8
              ) OR
              (
                  DATE(CONVERT_TZ(${field}, '+00:00', '+03:00')) > DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND
                  DATE(CONVERT_TZ(${field}, '+00:00', '+03:00')) <= CURDATE() AND
                  HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) < 4
              )
          )
      `;
      break;
    }
    case 'this_month': {
      // Bu ayın tüm iş günlerini (her gün 08:00-04:00) kapsamak için
      filter = `
          (
              (
                  YEAR(CONVERT_TZ(${field}, '+00:00', '+03:00')) = YEAR(NOW()) AND
                  MONTH(CONVERT_TZ(${field}, '+00:00', '+03:00')) = MONTH(NOW())
              ) OR
              (
                  YEAR(CONVERT_TZ(${field}, '+00:00', '+03:00')) = YEAR(DATE_ADD(NOW(), INTERVAL 1 DAY)) AND
                  MONTH(CONVERT_TZ(${field}, '+00:00', '+03:00')) = MONTH(DATE_ADD(NOW(), INTERVAL 1 DAY)) AND
                  DAY(CONVERT_TZ(${field}, '+00:00', '+03:00')) = 1 AND
                  HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) < 4
              )
          ) AND
          (
              (HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) >= 8) OR
              (HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) < 4)
          )
      `;
      break;
    }
    case 'last_month': {
      // Geçen ayın tüm iş günlerini (her gün 08:00-04:00) kapsamak için
      filter = `
          (
              (
                  YEAR(CONVERT_TZ(${field}, '+00:00', '+03:00')) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND
                  MONTH(CONVERT_TZ(${field}, '+00:00', '+03:00')) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
              ) OR
              (
                  YEAR(CONVERT_TZ(${field}, '+00:00', '+03:00')) = YEAR(DATE_ADD(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 DAY)) AND
                  MONTH(CONVERT_TZ(${field}, '+00:00', '+03:00')) = MONTH(DATE_ADD(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 DAY)) AND
                  DAY(CONVERT_TZ(${field}, '+00:00', '+03:00')) = 1 AND
                  HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) < 4
              )
          ) AND
          (
              (HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) >= 8) OR
              (HOUR(CONVERT_TZ(${field}, '+00:00', '+03:00')) < 4)
          )
      `;
      break;
    }
    default: {
      filter = '';
    }
  }

  return { params, filter };
};

/**
 * Tüm siparişleri, sipariş kalemlerini ve ödemeleri detaylı bir şekilde getirir
 * @param {Object} filters - Filtreleme seçenekleri
 * @param {string} filters.type - Filtre türü (today, yesterday, last_7days, this_month, last_month, custom)
 * @param {string} filters.startDate - Başlangıç tarihi (YYYY-MM-DD) - custom filtre türü için
 * @param {string} filters.endDate - Bitiş tarihi (YYYY-MM-DD) - custom filtre türü için
 * @param {string} filters.status - Sipariş durumu (optional)
 * @param {string} filters.paymentStatus - Ödeme durumu (optional)
 * @param {number} filters.tableId - Masa ID (optional)
 * @param {string} filters.customerId - Müşteri ID (optional)
 * @param {number} filters.floorId - Alan ID (optional)
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} Siparişler, sipariş kalemleri, ödemeler ve diğer detaylar
 */
exports.getAllOrderDetailsDB = async (filters, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Filtreleme koşullarını oluştur
    let whereConditions = ["o.tenant_id = ?"];
    let queryParams = [tenantId];

    // Tarih filtresi
    if (filters.type) {
      const { filter, params } = getFilterCondition('o.date', filters.type, filters.startDate, filters.endDate);
      if (filter) {
        whereConditions.push(filter);
        queryParams.push(...params);
      }
    } else if (filters.startDate && filters.endDate) {
      whereConditions.push("o.date BETWEEN ? AND ?");
      queryParams.push(filters.startDate + " 00:00:00", filters.endDate + " 23:59:59");
    }

    // Sipariş durumu filtresi
    if (filters.status) {
      whereConditions.push("o.status = ?");
      queryParams.push(filters.status);
    }

    // Ödeme durumu filtresi
    if (filters.paymentStatus) {
      whereConditions.push("o.payment_status = ?");
      queryParams.push(filters.paymentStatus);
    }

    // Masa filtresi
    if (filters.tableId) {
      whereConditions.push("o.table_id = ?");
      queryParams.push(filters.tableId);
    }

    // Müşteri filtresi
    if (filters.customerId) {
      whereConditions.push("o.customer_id = ?");
      queryParams.push(filters.customerId);
    }

    // Alan (floor) filtresi
    if (filters.floorId) {
      whereConditions.push("st.floor = ?");
      queryParams.push(filters.floorId);
    }

    // WHERE koşulunu oluştur
    const whereClause = whereConditions.join(" AND ");

    // 1. Siparişleri getir
    const ordersSql = `
      SELECT
        o.id,
        o.date,
        o.delivery_type,
        o.customer_type,
        o.customer_id,
        c.name AS customer_name,
        c.phone AS customer_phone,
        c.email AS customer_email,
        c.address AS customer_address,
        o.table_id,
        st.table_title,
        st.floor,
        o.status,
        o.payment_status,
        o.token_no,
        o.username,
        u.name AS user_name,
        o.remaining_balance,
        o.closed_at,
        o.invoice_id
      FROM
        orders o
        LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
        LEFT JOIN store_tables st ON o.table_id = st.id
        LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id
      WHERE
        ${whereClause}
      ORDER BY o.date DESC
    `;

    const [orders] = await conn.query(ordersSql, queryParams);

    // Eğer sipariş yoksa boş sonuç döndür
    if (orders.length === 0) {
      return {
        orders: [],
        orderItems: [],
        addons: [],
        payments: [],
        invoices: [],
        discounts: []
      };
    }

    // Sipariş ID'lerini al
    const orderIds = orders.map(o => o.id);
    const orderIdsStr = orderIds.join(",");

    // 2. Sipariş kalemlerini getir
    const orderItemsSql = `
      SELECT
        oi.id,
        oi.order_id,
        oi.item_id,
        mi.title AS item_title,
        mi.description AS item_description,
        oi.variant_id,
        miv.title as variant_title,
        miv.price as variant_price,
        oi.price AS order_item_price,
        mi.tax_id,
        t.title as tax_title,
        t.rate as tax_rate,
        t.type as tax_type,
        oi.quantity,
        oi.status,
        oi.date,
        oi.addons,
        oi.notes,
        oi.points,

        -- İptal bilgileri
        oi.cancelled_by,
        oi.cancelled_reason_id,
        cr.title AS cancelled_reason_title,

        -- İkram bilgileri
        oi.complimentary_by,
        oi.complimentary_reason_id,
        cmr.title AS complimentary_reason_title,

        -- Zayi bilgileri
        oi.waste_by,
        oi.waste_reason_id,
        wr.title AS waste_reason_title,

        -- İşlemi yapan kullanıcıların adları
        u_cancelled.name AS cancelled_by_name,
        u_complimentary.name AS complimentary_by_name,
        u_waste.name AS waste_by_name
      FROM
        order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN menu_item_variants miv ON oi.item_id = miv.item_id AND oi.variant_id = miv.id
        LEFT JOIN taxes t ON mi.tax_id = t.id

        -- İptal sebepleri ve kullanıcıları
        LEFT JOIN cancellation_reasons cr ON oi.cancelled_reason_id = cr.id
        LEFT JOIN users u_cancelled ON oi.cancelled_by = u_cancelled.username

        -- İkram sebepleri ve kullanıcıları
        LEFT JOIN complimentary_reasons cmr ON oi.complimentary_reason_id = cmr.id
        LEFT JOIN users u_complimentary ON oi.complimentary_by = u_complimentary.username

        -- Zayi sebepleri ve kullanıcıları
        LEFT JOIN waste_reasons wr ON oi.waste_reason_id = wr.id
        LEFT JOIN users u_waste ON oi.waste_by = u_waste.username
      WHERE
        oi.order_id IN (${orderIdsStr})
      ORDER BY oi.date ASC
    `;

    const [orderItems] = await conn.query(orderItemsSql);

    // 3. Eklentileri (addons) getir
    const addonIds = [...new Set(orderItems.flatMap(o => o.addons ? JSON.parse(o.addons || '[]') : []))];
    let addons = [];

    if (addonIds.length > 0) {
      const addonsSql = `
        SELECT
          id,
          item_id,
          title,
          price
        FROM
          menu_item_addons
        WHERE
          id IN (${addonIds.join(",")})
      `;

      const [addonsResult] = await conn.query(addonsSql);
      addons = addonsResult;
    }

    // 4. Ödeme işlemlerini getir (payment_transactions tablosundan)
    const paymentTransactionsSql = `
      SELECT
        pt.id,
        pt.order_id,
        pt.payment_type_id,
        pt.amount,
        pt.transaction_type,
        pt.status,
        pt.notes as product_name,
        pt.created_by,
        pt.created_at,
        pt.updated_by,
        pt.updated_at,
        pt.invoice_id,
        pt.cash_register_session_id,
        pt.tenant_id,
        pty.title as payment_type,
        pty.icon as payment_icon,
        u.name as user_name
      FROM
        payment_transactions pt
        JOIN payment_types pty ON pt.payment_type_id = pty.id
        LEFT JOIN users u ON pt.created_by = u.username
      WHERE
        pt.order_id IN (${orderIdsStr})
        AND pt.tenant_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'payment'
      ORDER BY pt.created_at ASC
    `;

    const [partialPayments] = await conn.query(paymentTransactionsSql, [tenantId]);

    // 5. Faturaları getir
    let invoices = [];
    const invoiceIds = orders.filter(o => o.invoice_id).map(o => o.invoice_id);

    if (invoiceIds.length > 0) {
      const invoicesSql = `
        SELECT
          i.id,
          i.created_at,
          i.sub_total,
          i.tax_total,
          i.total,
          i.tenant_id,
          i.payment_type_id,
          pt.title as payment_type,
          pt.icon as payment_icon
        FROM
          invoices i
          LEFT JOIN payment_types pt ON i.payment_type_id = pt.id
        WHERE
          i.id IN (${invoiceIds.join(",")})
          AND i.tenant_id = ?
        ORDER BY i.id DESC
      `;

      [invoices] = await conn.query(invoicesSql, [tenantId]);
    }

    // 6. İndirimleri getir
    const discountsSql = `
      SELECT
        od.id,
        od.order_id,
        od.order_item_id,
        od.discount_type,
        od.discount_value,
        od.created_at,
        od.tenant_id,
        od.created_by,
        u.name AS created_by_name
      FROM
        order_discounts od
        LEFT JOIN users u ON od.created_by = u.username AND u.tenant_id = od.tenant_id
      WHERE
        od.order_id IN (${orderIdsStr})
        AND od.tenant_id = ?
      ORDER BY od.id ASC
    `;

    const [discounts] = await conn.query(discountsSql, [tenantId]);

    // Sonuçları döndür
    return {
      orders,
      orderItems,
      addons,
      payments: partialPayments,
      invoices,
      discounts
    };
  } catch (error) {
    console.error("getAllOrderDetailsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
