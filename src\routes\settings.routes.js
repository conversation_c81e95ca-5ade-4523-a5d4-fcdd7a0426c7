const { Router } = require("express");

const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getStoreDetails,
  setStoreDetails,
  getPrintSettings,
  setPrintSettings,
  getAllTaxes,
  addTax,
  updateTax,
  deletTax,
  getTax,
  addPaymentType,
  getAllPaymentTypes,
  updatePaymentType,
  deletePaymentType,
  togglePaymentType,
  addStoreTable,
  bulkAddStoreTables,
  getAllStoreTables,
  updateStoreTable,
  deleteStoreTable,
  addCategory,
  getCategories,
  updateCategory,
  deleteCategory,
  uploadSlidesItemPhoto,
  removeSlidesItemPhoto,
  deleteStoreImage,
  uploadCategoryPhoto,
  uploadStoreImage,
  getfeedBacks,
  getTenantConfig,
  updateTenantConfig,
  clearTenantData,
  enableMaliMode,
  disableMaliMode,
  getMaliModeStatus,
  getYazarkasaSettings,
  updateYazarkasaSettings,
  getYazarkasaDevices,
  addYazarkasaDevice,
  updateYazarkasaDevice,
  deleteYazarkasaDevice,
  getYazarkasaDevice
} = require("../controllers/settings.controller");

const router = Router();

router.get(
  "/feedbacks",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getfeedBacks
);

router.delete(
  '/clear-data',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.VERI_SIL]),
  clearTenantData
);

router.get(
  "/store-setting",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getStoreDetails
);
router.post(
  "/store-setting",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  setStoreDetails
);

router.post(
  "/tenant-config/update",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateTenantConfig
);

router.get(
  "/tenant-config",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getTenantConfig
);

router.post(
  "/store-setting/upload-store-image",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  uploadStoreImage
);

router.post(
  "/store-setting/delete-store-image",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteStoreImage
);

router.post(
  "/update/upload-photo",
  isLoggedIn,
  isAuthenticated,
  authorize([SCOPES.SETTINGS]),
  uploadSlidesItemPhoto
);
router.post(
  "/update/remove-photo",
  isLoggedIn,
  isAuthenticated,
  authorize([SCOPES.SETTINGS]),
  removeSlidesItemPhoto
);

router.get(
  "/print-setting",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getPrintSettings
);
router.post(
  "/print-setting",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  setPrintSettings
);

router.get(
  "/taxes",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getAllTaxes
);
router.post(
  "/taxes/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addTax
);
router.post(
  "/taxes/:id/update",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateTax
);
router.get(
  "/taxes/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getTax
);
router.delete(
  "/taxes/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deletTax
);

router.post(
  "/payment-types/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addPaymentType
);
router.get(
  "/payment-types",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getAllPaymentTypes
);
router.post(
  "/payment-types/:id/update",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updatePaymentType
);
router.post(
  "/payment-types/:id/toggle",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  togglePaymentType
);
router.delete(
  "/payment-types/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deletePaymentType
);

router.post(
  "/store-tables/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addStoreTable
);
router.post(
  "/store-tables/bulk-add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  bulkAddStoreTables
);
router.get(
  "/store-tables",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getAllStoreTables
);
router.post(
  "/store-tables/:id/update",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateStoreTable
);
router.delete(
  "/store-tables/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteStoreTable
);

router.post(
  "/categories/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addCategory
);
router.get(
  "/categories",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getCategories
);
router.post(
  "/categories/:id/update",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateCategory
);

router.post(
  "/categories/:id/upload-photo",
  isLoggedIn,
  isAuthenticated,
  authorize([SCOPES.SETTINGS]),
  uploadCategoryPhoto
);

router.delete(
  "/categories/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteCategory
);

// Mali Mode Routes
router.post(
  "/mali-mode/enable",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  enableMaliMode
);

router.post(
  "/mali-mode/disable",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  disableMaliMode
);

router.get(
  "/mali-mode/status",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getMaliModeStatus
);

// ==================== YAZARKASA ENTEGRASYON ====================

// Yazarkasa entegrasyon ayarları
router.get(
  "/yazarkasa/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getYazarkasaSettings
);

router.put(
  "/yazarkasa/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateYazarkasaSettings
);

// ==================== YAZARKASA CİHAZ YÖNETİMİ ====================

// Yazarkasa cihazları listele
router.get(
  "/yazarkasa/devices",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getYazarkasaDevices
);

// Yazarkasa cihazı ekle
router.post(
  "/yazarkasa/devices",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addYazarkasaDevice
);

// Yazarkasa cihazı detayı getir
router.get(
  "/yazarkasa/devices/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getYazarkasaDevice
);

// Yazarkasa cihazı güncelle
router.put(
  "/yazarkasa/devices/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  updateYazarkasaDevice
);

// Yazarkasa cihazı sil
router.delete(
  "/yazarkasa/devices/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteYazarkasaDevice
);

module.exports = router;
