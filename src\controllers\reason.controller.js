const {
  getCancellationReasonsDB,
  getCancellationReasonByIdDB,
  addCancellationReasonDB,
  updateCancellationReasonDB,
  deleteCancellationReasonDB,
  getComplimentaryReasonsDB,
  getComplimentaryReasonByIdDB,
  addComplimentaryReasonDB,
  updateComplimentaryReasonDB,
  deleteComplimentaryReasonDB,
  getWasteReasonsDB,
  getWasteReasonByIdDB,
  addWasteReasonDB,
  updateWasteReasonDB,
  deleteWasteReasonDB
} = require("../services/reason.service");

// Cancellation Reasons
exports.getCancellationReasons = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const reasons = await getCancellationReasonsDB(tenantId);

    return res.status(200).json({
      success: true,
      data: reasons
    });
  } catch (error) {
    console.error("Error in getCancellationReasons:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.getCancellationReasonById = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getCancellationReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İptal nedeni bulunamadı."
      });
    }

    return res.status(200).json({
      success: true,
      data: reason
    });
  } catch (error) {
    console.error("Error in getCancellationReasonById:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.addCancellationReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { title, description, isActive = true } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reasonId = await addCancellationReasonDB(title, description, isActive, tenantId);

    return res.status(201).json({
      success: true,
      message: "İptal nedeni başarıyla eklendi.",
      data: { id: reasonId }
    });
  } catch (error) {
    console.error("Error in addCancellationReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.updateCancellationReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const { title, description, isActive } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reason = await getCancellationReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İptal nedeni bulunamadı."
      });
    }

    const success = await updateCancellationReasonDB(id, title, description, isActive, tenantId);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "İptal nedeni güncellenemedi."
      });
    }

    return res.status(200).json({
      success: true,
      message: "İptal nedeni başarıyla güncellendi."
    });
  } catch (error) {
    console.error("Error in updateCancellationReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.deleteCancellationReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getCancellationReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İptal nedeni bulunamadı."
      });
    }

    try {
      const success = await deleteCancellationReasonDB(id, tenantId);

      if (!success) {
        return res.status(400).json({
          success: false,
          message: "İptal nedeni silinemedi."
        });
      }

      return res.status(200).json({
        success: true,
        message: "İptal nedeni başarıyla silindi."
      });
    } catch (error) {
      if (error.message.includes("kullanımda")) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in deleteCancellationReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

// Complimentary Reasons
exports.getComplimentaryReasons = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const reasons = await getComplimentaryReasonsDB(tenantId);

    return res.status(200).json({
      success: true,
      data: reasons
    });
  } catch (error) {
    console.error("Error in getComplimentaryReasons:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.getComplimentaryReasonById = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getComplimentaryReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İkram nedeni bulunamadı."
      });
    }

    return res.status(200).json({
      success: true,
      data: reason
    });
  } catch (error) {
    console.error("Error in getComplimentaryReasonById:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.addComplimentaryReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { title, description, isActive = true } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reasonId = await addComplimentaryReasonDB(title, description, isActive, tenantId);

    return res.status(201).json({
      success: true,
      message: "İkram nedeni başarıyla eklendi.",
      data: { id: reasonId }
    });
  } catch (error) {
    console.error("Error in addComplimentaryReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.updateComplimentaryReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const { title, description, isActive } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reason = await getComplimentaryReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İkram nedeni bulunamadı."
      });
    }

    const success = await updateComplimentaryReasonDB(id, title, description, isActive, tenantId);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "İkram nedeni güncellenemedi."
      });
    }

    return res.status(200).json({
      success: true,
      message: "İkram nedeni başarıyla güncellendi."
    });
  } catch (error) {
    console.error("Error in updateComplimentaryReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.deleteComplimentaryReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getComplimentaryReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "İkram nedeni bulunamadı."
      });
    }

    try {
      const success = await deleteComplimentaryReasonDB(id, tenantId);

      if (!success) {
        return res.status(400).json({
          success: false,
          message: "İkram nedeni silinemedi."
        });
      }

      return res.status(200).json({
        success: true,
        message: "İkram nedeni başarıyla silindi."
      });
    } catch (error) {
      if (error.message.includes("kullanımda")) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in deleteComplimentaryReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

// Waste Reasons
exports.getWasteReasons = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const reasons = await getWasteReasonsDB(tenantId);

    return res.status(200).json({
      success: true,
      data: reasons
    });
  } catch (error) {
    console.error("Error in getWasteReasons:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.getWasteReasonById = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getWasteReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "Fire nedeni bulunamadı."
      });
    }

    return res.status(200).json({
      success: true,
      data: reason
    });
  } catch (error) {
    console.error("Error in getWasteReasonById:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.addWasteReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { title, description, isActive = true } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reasonId = await addWasteReasonDB(title, description, isActive, tenantId);

    return res.status(201).json({
      success: true,
      message: "Fire nedeni başarıyla eklendi.",
      data: { id: reasonId }
    });
  } catch (error) {
    console.error("Error in addWasteReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.updateWasteReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const { title, description, isActive } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Başlık alanı zorunludur."
      });
    }

    const reason = await getWasteReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "Fire nedeni bulunamadı."
      });
    }

    const success = await updateWasteReasonDB(id, title, description, isActive, tenantId);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "Fire nedeni güncellenemedi."
      });
    }

    return res.status(200).json({
      success: true,
      message: "Fire nedeni başarıyla güncellendi."
    });
  } catch (error) {
    console.error("Error in updateWasteReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

exports.deleteWasteReason = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const reason = await getWasteReasonByIdDB(id, tenantId);

    if (!reason) {
      return res.status(404).json({
        success: false,
        message: "Fire nedeni bulunamadı."
      });
    }

    try {
      const success = await deleteWasteReasonDB(id, tenantId);

      if (!success) {
        return res.status(400).json({
          success: false,
          message: "Fire nedeni silinemedi."
        });
      }

      return res.status(200).json({
        success: true,
        message: "Fire nedeni başarıyla silindi."
      });
    } catch (error) {
      if (error.message.includes("kullanımda")) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in deleteWasteReason:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};
