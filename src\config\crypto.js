// config/crypto.js
const crypto = require('crypto');
const SECRET_KEY = 'hT7kV9zp2Qf4mR8nXs5aYc3eLb0NdjU1'; // 32 bayt olmalı
module.exports.SECRET_KEY = SECRET_KEY;

module.exports.encrypt = function(text) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(SECRET_KEY), iv);
  const encrypted = Buffer.concat([cipher.update(text.toString()), cipher.final()]);
  // iv ve şifrelenmiş veriyi hex’le birleştir
  return iv.toString('hex') + ':' + encrypted.toString('hex');
};

module.exports.decrypt = function(encryptedData) {
  const [ivHex, encryptedHex] = encryptedData.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const encryptedText = Buffer.from(encryptedHex, 'hex');
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(SECRET_KEY), iv);
  const decrypted = Buffer.concat([decipher.update(encryptedText), decipher.final()]);
  return decrypted.toString();
};
