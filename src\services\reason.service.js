const { getMySqlPromiseConnection } = require("../config/mysql.db");

// Cancellation Reasons
exports.getCancellationReasonsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM cancellation_reasons
      WHERE tenant_id = ?
      ORDER BY title ASC
    `;

    const [reasons] = await conn.query(sql, [tenantId]);
    return reasons;
  } catch (error) {
    console.error("Error in getCancellationReasonsDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.getCancellationReasonByIdDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM cancellation_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [reasons] = await conn.query(sql, [id, tenantId]);
    return reasons.length > 0 ? reasons[0] : null;
  } catch (error) {
    console.error("Error in getCancellationReasonByIdDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.addCancellationReasonDB = async (title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO cancellation_reasons (title, description, is_active, tenant_id)
      VALUES (?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [title, description, isActive, tenantId]);
    return result.insertId;
  } catch (error) {
    console.error("Error in addCancellationReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateCancellationReasonDB = async (id, title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE cancellation_reasons
      SET title = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [title, description, isActive, id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in updateCancellationReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.deleteCancellationReasonDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Check if the reason is used in any orders
    const [usageCheck] = await conn.query(
      `SELECT COUNT(*) as count FROM order_cancellation_reasons WHERE reason_id = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    if (usageCheck[0].count > 0) {
      throw new Error("Bu iptal nedeni kullanımda olduğu için silinemez.");
    }

    const sql = `
      DELETE FROM cancellation_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in deleteCancellationReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Complimentary Reasons
exports.getComplimentaryReasonsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM complimentary_reasons
      WHERE tenant_id = ?
      ORDER BY title ASC
    `;

    const [reasons] = await conn.query(sql, [tenantId]);
    return reasons;
  } catch (error) {
    console.error("Error in getComplimentaryReasonsDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.getComplimentaryReasonByIdDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM complimentary_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [reasons] = await conn.query(sql, [id, tenantId]);
    return reasons.length > 0 ? reasons[0] : null;
  } catch (error) {
    console.error("Error in getComplimentaryReasonByIdDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.addComplimentaryReasonDB = async (title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO complimentary_reasons (title, description, is_active, tenant_id)
      VALUES (?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [title, description, isActive, tenantId]);
    return result.insertId;
  } catch (error) {
    console.error("Error in addComplimentaryReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateComplimentaryReasonDB = async (id, title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE complimentary_reasons
      SET title = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [title, description, isActive, id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in updateComplimentaryReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.deleteComplimentaryReasonDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Check if the reason is used in any order items
    const [usageCheck] = await conn.query(
      `SELECT COUNT(*) as count FROM order_items WHERE complimentary_reason_id = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    if (usageCheck[0].count > 0) {
      throw new Error("Bu ikram nedeni kullanımda olduğu için silinemez.");
    }

    const sql = `
      DELETE FROM complimentary_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in deleteComplimentaryReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Waste Reasons
exports.getWasteReasonsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM waste_reasons
      WHERE tenant_id = ?
      ORDER BY title ASC
    `;

    const [reasons] = await conn.query(sql, [tenantId]);
    return reasons;
  } catch (error) {
    console.error("Error in getWasteReasonsDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.getWasteReasonByIdDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, title, description, is_active, created_at, updated_at
      FROM waste_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [reasons] = await conn.query(sql, [id, tenantId]);
    return reasons.length > 0 ? reasons[0] : null;
  } catch (error) {
    console.error("Error in getWasteReasonByIdDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.addWasteReasonDB = async (title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO waste_reasons (title, description, is_active, tenant_id)
      VALUES (?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [title, description, isActive, tenantId]);
    return result.insertId;
  } catch (error) {
    console.error("Error in addWasteReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateWasteReasonDB = async (id, title, description, isActive, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE waste_reasons
      SET title = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [title, description, isActive, id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in updateWasteReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.deleteWasteReasonDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Check if the reason is used in any order items
    const [usageCheck] = await conn.query(
      `SELECT COUNT(*) as count FROM order_items WHERE waste_reason_id = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    if (usageCheck[0].count > 0) {
      throw new Error("Bu fire nedeni kullanımda olduğu için silinemez.");
    }

    const sql = `
      DELETE FROM waste_reasons
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in deleteWasteReasonDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};
