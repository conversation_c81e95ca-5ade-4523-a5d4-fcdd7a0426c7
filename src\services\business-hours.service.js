const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * Tenant'ın çalışma saatlerini getirir
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Çalışma saatleri bilgisi
 */
exports.getBusinessHoursDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT 
        id,
        tenant_id,
        day_start_time,
        day_end_time,
        is_overnight,
        is_active,
        created_at,
        updated_at
      FROM business_hours 
      WHERE tenant_id = ? AND is_active = 1
    `;

    const [result] = await conn.query(sql, [tenantId]);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error("getBusinessHoursDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Tenant'ın çalışma saatlerini günceller veya oluşturur
 * @param {number} tenantId - Kiracı ID
 * @param {Object} businessHours - Çalışma saatleri bilgisi
 * @param {string} businessHours.day_start_time - Gün başlangıç saati (HH:MM:SS)
 * @param {string} businessHours.day_end_time - Gün bitiş saati (HH:MM:SS)
 * @param {boolean} businessHours.is_overnight - Gece yarısını geçen işletme mi?
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updateBusinessHoursDB = async (tenantId, businessHours) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { day_start_time, day_end_time, is_overnight } = businessHours;

    // Önce mevcut kayıt var mı kontrol et
    const [existing] = await conn.query(
      "SELECT id FROM business_hours WHERE tenant_id = ?",
      [tenantId]
    );

    if (existing.length > 0) {
      // Güncelle
      const updateSql = `
        UPDATE business_hours 
        SET 
          day_start_time = ?,
          day_end_time = ?,
          is_overnight = ?,
          updated_at = NOW()
        WHERE tenant_id = ?
      `;

      const [result] = await conn.query(updateSql, [
        day_start_time,
        day_end_time,
        is_overnight ? 1 : 0,
        tenantId
      ]);

      return result.affectedRows > 0;
    } else {
      // Yeni kayıt oluştur
      const insertSql = `
        INSERT INTO business_hours 
        (tenant_id, day_start_time, day_end_time, is_overnight) 
        VALUES (?, ?, ?, ?)
      `;

      const [result] = await conn.query(insertSql, [
        tenantId,
        day_start_time,
        day_end_time,
        is_overnight ? 1 : 0
      ]);

      return result.insertId > 0;
    }
  } catch (error) {
    console.error("updateBusinessHoursDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Tenant'ın çalışma saatlerini siler
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deleteBusinessHoursDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = "UPDATE business_hours SET is_active = 0 WHERE tenant_id = ?";
    const [result] = await conn.query(sql, [tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("deleteBusinessHoursDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Belirli bir saatin çalışma saatleri içinde olup olmadığını kontrol eder
 * @param {number} tenantId - Kiracı ID
 * @param {string} checkTime - Kontrol edilecek saat (HH:MM:SS veya Date)
 * @returns {boolean} Çalışma saatleri içinde mi?
 */
exports.isWithinBusinessHoursDB = async (tenantId, checkTime = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const businessHours = await exports.getBusinessHoursDB(tenantId);
    
    if (!businessHours) {
      // Çalışma saatleri tanımlanmamışsa her zaman true döndür
      return true;
    }

    // Kontrol edilecek saati belirle
    const now = checkTime ? new Date(checkTime) : new Date();
    const currentTime = now.toTimeString().split(' ')[0]; // HH:MM:SS formatında

    const startTime = businessHours.day_start_time;
    const endTime = businessHours.day_end_time;
    const isOvernight = businessHours.is_overnight;

    if (isOvernight) {
      // Gece yarısını geçen işletme (örn: 16:00-05:00)
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      // Normal işletme (örn: 08:00-23:59)
      return currentTime >= startTime && currentTime <= endTime;
    }
  } catch (error) {
    console.error("isWithinBusinessHoursDB error:", error);
    // Hata durumunda true döndür (güvenli taraf)
    return true;
  } finally {
    conn.release();
  }
};
