const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getTranslations,
  deleteTranslation,
  addOrUpdateTranslation,
  addOrUpdateMultipleTranslations,
  deleteAllTranslationsForObject,
  autoTranslate,
} = require("../controllers/translations.controller");

const router = Router();

// Tüm çevirileri getir
router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getTranslations
);

// Tek bir çeviri sil (ID ile)
router.delete(
  "/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteTranslation
);

// Tek bir çeviri ekle veya güncelle
router.post(
  "/save",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addOrUpdateTranslation
);

// Çoklu çeviri ekle veya güncelle
router.post(
  "/save-multiple",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  addOrUpdateMultipleTranslations
);

// Belirli bir nesne için tüm çevirileri sil
router.post(
  "/delete-all-for-object",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteAllTranslationsForObject
);

router.post(
  "/auto-translate",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  autoTranslate
);

module.exports = router;