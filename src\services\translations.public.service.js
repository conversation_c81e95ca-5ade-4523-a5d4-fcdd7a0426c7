const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.getPublicTranslationsDB = async (tenantId, objectType, languageCode) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let sql = `
      SELECT object_id, translation
      FROM translations
      WHERE object_type = ? AND language_code = ?
    `;
    const params = [objectType, languageCode];
    
    if (tenantId) {
      sql += " AND tenant_id = ?";
      params.push(tenantId);
    }

    sql += " ORDER BY object_id ASC";

    const [result] = await conn.query(sql, params);
    return result.reduce((acc, row) => {
      acc[row.object_id] = row.translation;
      return acc;
    }, {});
  } catch (error) {
    console.error("getPublicTranslationsDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};