const { getMySqlPromiseConnection } = require("../config/mysql.db")

/**
 * Sipariş ID'sine göre sipariş öğelerinin puanlarını toplar
 * @param {number} orderId - Sipariş ID
 * @returns {Object} Toplam puan bilgisi
 */
exports.collectOrderPointsDB = async (orderId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Siparişin varlığını ve durumunu kontrol et
    const [orderResult] = await conn.query(
      `SELECT id, status FROM orders WHERE id = ?`,
      [orderId]
    );

    if (orderResult.length === 0) {
      return {
        status: "failed",
        message: "Geçersiz QR kod"
      };
    }

    const order = orderResult[0];

    if (order.status !== 'completed') {
      return {
        status: "failed",
        message: "Sadece tamamlanmış siparişler için puan toplanabilir"
      };
    }

    // Ürünleri çek
    const [itemsResult] = await conn.query(
      `SELECT 
        m.title AS itemName,
        oi.quantity,
        oi.points,
        (oi.points * oi.quantity) AS totalPoints
      FROM order_items oi
      LEFT JOIN menu_items m ON oi.item_id = m.id
      WHERE oi.order_id = ? AND oi.status NOT IN ('cancelled', 'waste')`,
      [orderId]
    );

    const items = itemsResult || [];

    // Toplam puanı hesapla
    const totalPoints = items.reduce((sum, item) => sum + (item.totalPoints || 0), 0);

    // Message cümlesini hazırla
    const itemsMessage = items.map(item =>
      `${item.itemName} (${item.quantity} adet - ${item.totalPoints} puan)`
    ).join(", ");

    const message = `Toplam ${totalPoints} puan kazandınız! Ürünler: ${itemsMessage}`;


    return {
      status: "success",
      message,
      orderId,
      totalPoints,
      items
    };
  } catch (error) {
    console.error("Sipariş puanları toplanırken hata oluştu:", error);
    throw error;
  } finally {
    conn.release();
  }
};


