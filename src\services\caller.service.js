const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.saveCallDB = async (callData) => {
    try {
        const connection = await getMySqlPromiseConnection();
        
        const query = `
            INSERT INTO calls (
                tenant_id,
                phone_number,
                call_type,
                device_info,
                created_at
            ) VALUES (?, ?, ?, ?, ?)
        `;

        const values = [
            callData.tenant_id,
            callData.phone_number,
            callData.call_type,
            callData.device_info || null,
            callData.created_at
        ];

        console.log("SQL Sorgusu:", query);
        console.log("<PERSON><PERSON><PERSON><PERSON>:", values);

        const [result] = await connection.execute(query, values);

        // Kaydedilen veriyi getir
        const [insertedCall] = await connection.execute(
            'SELECT * FROM calls WHERE id = ?',
            [result.insertId]
        );

        return insertedCall[0];
    } catch (error) {
        console.error('Database error in saveCallDB:', error);
        throw error;
    }
};

exports.getCallsDB = async (tenantId) => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            SELECT * FROM calls 
            WHERE tenant_id = ? 
            ORDER BY created_at DESC
        `;
        
        const [calls] = await connection.execute(query, [tenantId]);
        return calls;
    } catch (error) {
        console.error('Database error in getCallsDB:', error);
        throw error;
    }
};