const { getOrdersDB, saveDiscountDB,  updateOrderTableAndStatusDB,
savePartialPaymentforOrdersDB, moveOrderItemToTableDB, mergeTablesDB, updateOrderItemAsComplimentaryDB, updateOrderItemAsWasteDB, completeOrdersAndSaveCariIdDB, updateOrderItemStatusDB, updateOrderItemPriceDB, updateTableStatusDB, cancelOrderDB, completeOrderDB, savePartialPaymentDB, getPartialPaymentsDB , getOrdersPaymentSummaryDB, createInvoiceDB, completeOrdersAndSaveInvoiceIdDB } = require("../services/orders.service");
const {
  getPaymentTypesDB,
  getPrintSettingDB,
  getStoreSettingDB,
  getYazarkasaSettingsDB,
  getYazarkasaDevicesDB
} = require("../services/settings.service");
const axios = require('axios');

// Yazarkasa entegrasyonu için yardımcı fonksiyon
const processYazarkasaPayment = async (tenantId, orderData, paymentData) => {
  try {
    // Yazarkasa ayarlarını kontrol et
    const yazarkasaSettings = await getYazarkasaSettingsDB(tenantId);
    if (!yazarkasaSettings.is_active) {
      console.log('Yazarkasa entegrasyonu pasif, işlem atlanıyor');
      return { success: true, skipped: true };
    }

    // Aktif cihazları getir
    const devices = await getYazarkasaDevicesDB(tenantId);
    const activeDevices = devices.filter(device => device.is_active);

    if (activeDevices.length === 0) {
      console.log('Aktif yazarkasa cihazı bulunamadı');
      return { success: false, error: 'Aktif yazarkasa cihazı bulunamadı' };
    }

    // İlk aktif cihazı kullan
    const device = activeDevices[0];

    // Sipariş ürünlerini yazarkasa formatına çevir
    const items = orderData.items.map(item => ({
      name: item.title || item.name,
      unitPrice: Math.round((item.price || 0) * 100), // Kuruş cinsine çevir
      quantity: Math.round((item.quantity || 1) * 1000), // Yazarkasa quantity formatı
      discountRate: 0,
      discountAmount: 0,
      section: 1,
      unit: 0
    }));

    // Ödeme tipini yazarkasa formatına çevir (1: Nakit, 2: Kredi Kartı, vb.)
    let paymentType = 1; // Varsayılan nakit
    if (paymentData.paymentTypeName) {
      const paymentTypeName = paymentData.paymentTypeName.toLowerCase();
      if (paymentTypeName.includes('kart') || paymentTypeName.includes('card')) {
        paymentType = 2; // Kredi kartı
      }
    }

    // Yazarkasa API request body
    const requestBody = {
      deviceConfig: {
        deviceId: device.device_serial,
        deviceName: device.device_name,
        serialNo: device.device_serial,
        ipAddress: device.ip_address,
        port: parseInt(device.port),
        priority: 1,
        isEnabled: true,
        maxConcurrentSales: 1
      },
      saleData: {
        items: items,
        paymentType: paymentType,
        totalAmount: Math.round((paymentData.totalAmount || 0) * 100), // Kuruş cinsine çevir
        cashierName: paymentData.cashierName || 'Kasiyer',
        posReferenceId: `POS-${Date.now()}-${orderData.orderIds.join('-')}`,
        isPartialPayment: false
      },
      autoSelectDevice: false,
      skipDevicePreparation: false
    };

    console.log('Yazarkasa API isteği gönderiliyor:', JSON.stringify(requestBody, null, 2));

    // Yazarkasa API'ye istek gönder
    const response = await axios.post('http://localhost:5000/api/smartsales/smart-process', requestBody, {
      timeout: 30000, // 30 saniye timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Yazarkasa API yanıtı:', response.data);

    if (response.data && response.data.success) {
      return { success: true, data: response.data };
    } else {
      return {
        success: false,
        error: response.data?.errorMessage || response.data?.message || 'Yazarkasa işlemi başarısız',
        errorCode: response.data?.errorCode,
        details: response.data
      };
    }

  } catch (error) {
    console.error('Yazarkasa API hatası:', error);

    // HTTP hata kodları için response data'sını kontrol et
    if (error.response && error.response.data) {
      const errorData = error.response.data;
      console.log('Yazarkasa API hata detayı:', errorData);

      return {
        success: false,
        error: errorData.errorMessage || errorData.message || `HTTP ${error.response.status}: ${error.response.statusText}`,
        errorCode: errorData.errorCode,
        httpStatus: error.response.status,
        details: errorData
      };
    }

    // Network hataları
    if (error.code === 'ECONNREFUSED') {
      return { success: false, error: 'Yazarkasa servisi bağlantı hatası - Servis çalışmıyor olabilir' };
    } else if (error.code === 'ETIMEDOUT') {
      return { success: false, error: 'Yazarkasa servisi zaman aşımı' };
    } else {
      return { success: false, error: error.message || 'Yazarkasa işlemi hatası' };
    }
  }
};


exports.updateTableStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const tableId = req.params.tableId;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ success: false, message: 'Status is required' });
    }

    const result = await updateTableStatusDB(tableId, status, tenantId);

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Table not found or no permission' });
    }

    return res.status(200).json({ success: true, message: 'Table status updated successfully' });
  } catch (err) {
    console.error('Error updating table status:', err);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};


exports.updateOrderTable = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { orderId, newTableId, floorId } = req.body;

    if (!orderId || !newTableId) {
      return res.status(400).json({
        success: false,
        message: "Order ID and new Table ID are required"
      });
    }

    const result = await updateOrderTableAndStatusDB(orderId, newTableId, tenantId, floorId);

    if (result.status === "failed") {
      return res.status(400).json({ success: false, message: result.message });
    }

    return res.status(200).json({
      success: true,
      message: "Order table updated successfully"
    });
  } catch (error) {
    console.error("Error updating order table:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong! Please try again later."
    });
  }
};

exports.getOrders = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const { kitchenOrders, kitchenOrdersItems, addons, partialPayments } = await getOrdersDB(tenantId);

    const formattedOrders = kitchenOrders.map((order) => {
      const orderItems = kitchenOrdersItems.filter((oi) => oi.order_id == order.id);

      // Addons'u order items'a ekleme
      orderItems.forEach((oi, index) => {
        const addonsIds = oi?.addons ? JSON.parse(oi?.addons) : null;

        if (addonsIds) {
          const itemAddons = addonsIds.map((addonId) => {
            const addon = addons.filter((a) => a.id == addonId);
            return addon[0];
          });
          orderItems[index].addons = [...itemAddons];
        }
      });

      // Partial Payments'i siparişe ekleme
      const orderPartialPayments = partialPayments.filter((pp) => pp.order_id === order.id);

      return {
        ...order,
        items: orderItems,
        partialPayments: orderPartialPayments, // Yeni ekleme
      };
    });

    // group orders based on table id
    let ordersGroupedByTable = [];

    for (const order of formattedOrders) {
      const tableId = order.table_id;

      if (!tableId) {
        ordersGroupedByTable.push({
          table_id: tableId,
          table_title: order.table_title,
          floor: order.floor,
          orders: [{ ...order }],
          order_ids: [order.id],
        });
        continue;
      }

      const orderIndex = ordersGroupedByTable.findIndex((o) => o.table_id == tableId);
      if (orderIndex == -1) {
        ordersGroupedByTable.push({
          table_id: tableId,
          table_title: order.table_title,
          floor: order.floor,
          orders: [{ ...order }],
          order_ids: [order.id],
        });
      } else {
        ordersGroupedByTable[orderIndex].orders.push({ ...order });
        ordersGroupedByTable[orderIndex].order_ids.push(order.id);
      }
    }

    return res.status(200).json(ordersGroupedByTable);
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};


exports.getTableOrders = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const tableId = req.body.tableId;

    // Validasyon: tableId kontrolü
    if (!tableId || tableId.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request! Table ID is required.",
      });
    }

    // Veritabanından sipariş verilerini al
    const { kitchenOrders, kitchenOrdersItems, addons } = await getOrdersDB(tenantId);

    // İlgili tabloya ait siparişleri filtrele
    const tableOrders = kitchenOrders.filter((order) => order.table_id === tableId);

    if (tableOrders.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No orders found for the specified table.",
      });
    }

    // Siparişlere ait ürünleri eşleştir ve ekleri ekle
    const formattedOrders = tableOrders.map((order) => {
      const orderItems = kitchenOrdersItems.filter((oi) => oi.order_id === order.id);

      orderItems.forEach((oi, index) => {
        const addonsIds = oi?.addons ? JSON.parse(oi.addons) : null;

        if (addonsIds) {
          const itemAddons = addonsIds.map((addonId) => {
            const addon = addons.find((a) => a.id === addonId);
            return addon;
          });
          orderItems[index].addons = itemAddons || [];
        }
      });

      return {
        ...order,
        items: orderItems,
      };
    });

    // Döndürülecek veriyi yapılandır
    const response = {
      table_id: tableId,
      orders: formattedOrders,
    };

    return res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error in getOrdersTable:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong! Please try again later.",
    });
  }
};





exports.getPartialPayments = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { orderIds } = req.body;

    const payments = await getPartialPaymentsDB(orderIds, tenantId);

    return res.status(200).json(payments);
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong!"
    });
  }
};

exports.applyDiscount = async (req, res) => {
  try {
      const tenantId = req.user.tenant_id;
      const username = req.user.username;
      const { order_id, order_item_id, discount_type, discount_value } = req.body;

      if (!order_id || !discount_type || !discount_value) {
          return res.status(400).json({
              success: false,
              message: "Eksik bilgi!"
          });
      }

      await saveDiscountDB({
          order_id,
          order_item_id,
          discount_type,
          discount_value
      }, tenantId, username);

      return res.status(200).json({
          success: true,
          message: "İndirim başarıyla uygulandı!"
      });
  } catch (error) {
      console.error(error);
      return res.status(500).json({
          success: false,
          message: "Bir hata oluştu!"
      });
  }
};

exports.getOrdersInit = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const [paymentTypes, printSettings, storeSettings] = await Promise.all([
      getPaymentTypesDB(true, tenantId, true), // forPOS=true to filter out hideOnPos=1
      getPrintSettingDB(tenantId),
      getStoreSettingDB(tenantId),
    ]);

    return res.status(200).json({
      paymentTypes,
      printSettings,
      storeSettings,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateOrderItemPrice = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const orderItemId = req.params.id;
    const { newPrice } = req.body

    if(!newPrice) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!"
      });
    }

    await updateOrderItemPriceDB(orderItemId, newPrice, tenantId)

    return res.status(200).json({
      success: true,
      message: "Siparişteki Ürün Durumu Değişti"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateKitchenOrderItemStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const orderItemId = req.params.id;
    const { status, reasonId } = req.body;

    if(!status) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!"
      });
    }

    await updateOrderItemStatusDB(orderItemId, status, tenantId, username, reasonId)

    return res.status(200).json({
      success: true,
      message: "Siparişteki Ürün Durumu Değişti"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.cancelKitchenOrder = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const { orderIds, reasonId, notes } = req.body;

    if(!orderIds || orderIds?.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Sipariş ID'leri gereklidir!"
      });
    }

    if(!reasonId) {
      return res.status(400).json({
        success: false,
        message: "İptal nedeni seçilmelidir!"
      });
    }

    await cancelOrderDB(orderIds, reasonId, notes, username, tenantId);

    return res.status(200).json({
      success: true,
      message: "Sipariş başarıyla iptal edildi!"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.completeKitchenOrder = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { orderIds } = req.body

    if(!orderIds || orderIds?.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!"
      });
    }

    await completeOrderDB(orderIds, tenantId);

    return res.status(200).json({
      success: true,
      message: "Sipariş Başarıyla Tamamlandı!"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.getOrdersPaymentSummary = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const orderIds = req.body.orderIds;

    if (!orderIds || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!",
      });
    }

    const orderIdsParams = orderIds.join(",");

    // Veritabanından sipariş verilerini çekiyoruz
    const { kitchenOrders, kitchenOrdersItems, discounts } = await getOrdersPaymentSummaryDB(orderIdsParams, tenantId);
    const partialPayments = await getPartialPaymentsDB(orderIds, tenantId);

    const formattedOrders = kitchenOrders.map((order) => {
      const orderItems = kitchenOrdersItems.filter((oi) => oi.order_id === order.id);
      const orderDiscounts = discounts.filter((d) => d.order_id === order.id);

      orderItems.forEach((oi, index) => {
        const itemDiscounts = orderDiscounts.filter((d) => d.order_item_id === oi.id);
        orderItems[index].discounts = itemDiscounts;
      });

      const orderPartialPayments = partialPayments.filter((pp) => pp.order_id === order.id);
      const generalDiscounts = orderDiscounts.filter((d) => d.order_item_id === null);

      return {
        ...order,
        items: orderItems,
        partialPayments: orderPartialPayments,
        discounts: generalDiscounts,
      };
    });

    // **Hesaplama kısmı**
    let subtotal = 0;
    let taxTotal = 0;
    let total = 0;
    let discountTotal = 0;
    let totalPaid = 0; // **Parçalı ödenmiş toplam tutar**

    for (const order of formattedOrders) {
      const items = order.items;

      // **Sipariş geneli indirimleri hesapla**
      const orderDiscountAmount = order.discounts.reduce((total, discount) => {
        const discountValue = parseFloat(discount.discount_value) || 0;
        if (discount.discount_type === "percentage") {
          return total + (order.total * (discountValue / 100));
        }
        return total + discountValue;
      }, 0);

      for (let index = 0; index < items.length; index++) {
        const item = items[index];

        const { order_item_price, tax_rate, tax_type: taxType, quantity, discounts: itemDiscounts } = item;
        const taxRate = parseFloat(tax_rate) || 0;

        // **Toplam ürün fiyatı**
        const itemWithQuantity = parseFloat(order_item_price) * parseInt(quantity);

        // **Ürün indirimlerini hesapla**
        const itemDiscountAmount = itemDiscounts.reduce((total, discount) => {
          const discountValue = parseFloat(discount.discount_value) || 0;
          if (discount.discount_type === "percentage") {
            return total + (itemWithQuantity * (discountValue / 100));
          }
          return total + discountValue;
        }, 0);

        // **Toplam indirimi güncelle**
        discountTotal += itemDiscountAmount;

        if (taxType === "exclusive") {
          const tax = ((itemWithQuantity - itemDiscountAmount) * taxRate) / 100;
          const priceWithTax = itemWithQuantity + tax - itemDiscountAmount;

          taxTotal += tax;
          subtotal += itemWithQuantity;
          total += priceWithTax;

          items[index].itemTotal = priceWithTax / quantity;
          items[index].price = priceWithTax / quantity;
        } else if (taxType === "inclusive") {
          const tax = itemWithQuantity - (itemWithQuantity * (100 / (100 + taxRate)));
          const priceWithoutTax = itemWithQuantity - tax;

          taxTotal += tax;
          subtotal += priceWithoutTax - itemDiscountAmount;
          total += itemWithQuantity - itemDiscountAmount;

          items[index].itemTotal = (itemWithQuantity - itemDiscountAmount) / quantity;
          items[index].price = (itemWithQuantity - itemDiscountAmount) / quantity;
        } else {
          subtotal += itemWithQuantity - itemDiscountAmount;
          total += itemWithQuantity - itemDiscountAmount;

          items[index].itemTotal = (itemWithQuantity - itemDiscountAmount) / quantity;
          items[index].price = (itemWithQuantity - itemDiscountAmount) / quantity;
        }
      }

      // **Sipariş geneli indirimini toplam tutardan düş**
      total -= orderDiscountAmount;
      discountTotal += orderDiscountAmount;

      // **Siparişe ait parçalı ödenmiş tutarı hesapla**
      const orderPaidAmount = order.partialPayments.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);
      totalPaid += orderPaidAmount;
    }

    // **Toplamdan parçalı ödeme düşülüyor**
    total -= totalPaid;

    return res.status(200).json({
      subtotal,
      taxTotal,
      total, // **Parçalı ödeme düşülmüş toplam tutar**
      discountTotal, // Yeni eklenen toplam indirim tutarı
      totalPaid, // **Parçalı ödenmiş toplam tutar**
      orders: formattedOrders,
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.savePartialPayment = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const { payments } = req.body;

    if (!payments?.length) {
      return res.status(400).json({
        success: false,
        message: "Geçersiz istek!"
      });
    }

    // Kullanıcının aktif kasa oturumunu kontrol et
    const { getUserActiveSessionDB } = require('../services/cash-register.service');
    const activeSession = await getUserActiveSessionDB(username, tenantId);

    if (!activeSession) {
      return res.status(400).json({
        success: false,
        message: "Ödeme işlemi yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir"
      });
    }

    await savePartialPaymentDB(
      payments.map(payment => ({
        method: payment.method,
        amount: payment.amount,
        productName: payment.productName || null,
        orderItem_id: payment.orderItem_id || null,
        order_id: payment.order_id || null // Her ödeme için order_id bilgisi
      })),
      tenantId,
      username
    );

    // Socket bildirimi gönder
    if (global.io) {
      try {
        const orderIds = [...new Set(payments.map(p => p.order_id))];
        global.io.to(tenantId.toString()).emit('partial-payment-added', {
          orderIds,
          username,
          cashRegisterSessionId: activeSession.id
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(200).json({
      success: true,
      message: "Parçalı ödeme başarıyla kaydedildi!"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};


exports.payAndCompleteKitchenOrder = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const { orderIds, subTotal, taxTotal, total, selectedPaymentType, payments } = req.body;

    console.log("orderIds:", orderIds);
    console.log("payments:", payments);

    if (!orderIds || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!",
      });
    }

    // Kullanıcının aktif kasa oturumunu kontrol et
    const { getUserActiveSessionDB } = require('../services/cash-register.service');
    const activeSession = await getUserActiveSessionDB(username, tenantId);

    if (!activeSession) {
      return res.status(400).json({
        success: false,
        message: "Ödeme işlemi yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir"
      });
    }

    // Eğer payments array'i varsa onu kullan, yoksa eski sistemi kullan
    let paymentsToSave;

    if (payments && Array.isArray(payments) && payments.length > 0) {
      // Yeni sistem: Birden fazla ödeme türü
      paymentsToSave = payments.map(payment => ({
        method: payment.method,
        amount: payment.amount,
        productName: null,
        orderItem_id: null,
        order_id: orderIds.length === 1 ? orderIds[0] : null
      }));
    } else {
      // Eski sistem: Tek ödeme türü (backward compatibility)
      paymentsToSave = [
        {
          method: selectedPaymentType,
          amount: total,
          productName: null,
          orderItem_id: null,
          order_id: orderIds.length === 1 ? orderIds[0] : null
        }
      ];
    }

    // Yazarkasa entegrasyonu için sipariş verilerini hazırla
    let orderItems = [];
    try {
      // Sipariş ürünlerini getir
      const { getOrderItemsForYazarkasaDB } = require('../services/orders.service');
      orderItems = await getOrderItemsForYazarkasaDB(orderIds, tenantId);
    } catch (error) {
      console.error('Sipariş ürünleri alınamadı:', error);
    }

    // Ödeme tipinin adını getir
    let paymentTypeName = '';
    try {
      const paymentTypes = await getPaymentTypesDB(tenantId);
      const paymentType = paymentTypes.find(pt => pt.id === selectedPaymentType);
      paymentTypeName = paymentType ? paymentType.title : '';
    } catch (error) {
      console.error('Ödeme tipi bilgisi alınamadı:', error);
    }

    // Yazarkasa entegrasyonu
    let yazarkasaResult = null;
    try {
      yazarkasaResult = await processYazarkasaPayment(tenantId, {
        orderIds,
        items: orderItems
      }, {
        totalAmount: total,
        cashierName: username,
        paymentTypeName
      });

      console.log('Yazarkasa sonucu:', yazarkasaResult);

      // Yazarkasa başarısızsa işlemi durdur
      if (yazarkasaResult && !yazarkasaResult.success && !yazarkasaResult.skipped) {
        return res.status(400).json({
          success: false,
          message: "Yazarkasa cihazına ulaşılamıyor. Lütfen cihaz bağlantısını kontrol edin.",
          yazarkasaError: true
        });
      }
    } catch (error) {
      console.error('Yazarkasa işlemi hatası:', error);
      return res.status(500).json({
        success: false,
        message: "Yazarkasa entegrasyonu sırasında bir hata oluştu.",
        yazarkasaError: true
      });
    }

    // Yazarkasa başarılıysa veya atlandıysa normal ödeme işlemini tamamla
    await savePartialPaymentDB(paymentsToSave, tenantId, username);

    await completeOrdersAndSaveInvoiceIdDB(orderIds, tenantId);

    return res.status(200).json({
      success: true,
      message: "Sipariş Başarıyla Tamamlandı!",
      yazarkasa: yazarkasaResult ? {
        processed: !yazarkasaResult.skipped,
        success: yazarkasaResult.success,
        message: yazarkasaResult.skipped ? 'Yazarkasa entegrasyonu pasif' : 'Yazarkasa işlemi başarılı'
      } : null
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};



// Örneğin payAndCompleteCariOrder fonksiyonunuz:
exports.payAndCompleteCariOrder = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    // req.body'den müşteri bilgisini de alıyoruz:
    const { orderIds, subTotal, taxTotal, total, selectedPaymentType, payments, customer } = req.body;

    console.log("Received customer:", customer); // Debug log

    if (!orderIds || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid Request!"
      });
    }
    if (!customer) {
      return res.status(400).json({
        success: false,
        message: "Müşteri bilgisi eksik!"
      });
    }

    // İlgili fonksiyonu çağırıyoruz:
    await completeOrdersAndSaveCariIdDB(orderIds, tenantId, total, customer);

    return res.status(200).json({
      success: true,
      message: "Sipariş Başarıyla Tamamlandı!"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: error.message || "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateOrderItemAsComplimentary = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const orderItemId = req.params.id;
    const { status, reasonId } = req.body;

    if(status !== "complimentary") {
      return res.status(400).json({
        success: false,
        message: "Geçersiz İstek!"
      });
    }

    if(!reasonId) {
      return res.status(400).json({
        success: false,
        message: "İkram nedeni seçilmelidir!"
      });
    }

    await updateOrderItemAsComplimentaryDB(orderItemId, reasonId, tenantId, username);

    return res.status(200).json({
      success: true,
      message: "Ürün ikramlık olarak işaretlendi"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateOrderItemAsWaste = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const orderItemId = req.params.id;
    const { status, reasonId } = req.body;

    if(status !== "waste") {
      return res.status(400).json({
        success: false,
        message: "Geçersiz İstek!"
      });
    }

    if(!reasonId) {
      return res.status(400).json({
        success: false,
        message: "Fire nedeni seçilmelidir!"
      });
    }

    await updateOrderItemAsWasteDB(orderItemId, reasonId, tenantId, username);

    return res.status(200).json({
      success: true,
      message: "Ürün fire olarak işaretlendi"
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.moveOrderItemToTable = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { orderItemId, targetTableId } = req.body;

    if (!orderItemId || !targetTableId) {
      return res.status(400).json({
        success: false,
        message: "Sipariş öğesi ID ve hedef masa ID gereklidir"
      });
    }

    const result = await moveOrderItemToTableDB(orderItemId, targetTableId, tenantId);

    if (result.status === "failed") {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }

    return res.status(200).json({
      success: true,
      message: "Sipariş öğesi başarıyla taşındı",
      data: result
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
    });
  }
};

/**
 * İki masayı birleştirir
 */
exports.mergeTables = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { sourceTableId, targetTableId } = req.body;

    if (!sourceTableId || !targetTableId) {
      return res.status(400).json({
        success: false,
        message: "Kaynak masa ID ve hedef masa ID gereklidir"
      });
    }

    if (sourceTableId === targetTableId) {
      return res.status(400).json({
        success: false,
        message: "Kaynak ve hedef masa aynı olamaz"
      });
    }

    const result = await mergeTablesDB(sourceTableId, targetTableId, tenantId);

    if (result.status === "failed") {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }

    return res.status(200).json({
      success: true,
      message: "Masalar başarıyla birleştirildi",
      data: result
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
    });
  }
};
