const express = require('express');
const router = express.Router();
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getAllCashRegisters,
  getCashRegisterById,
  addCashRegister,
  updateCashRegister,
  deleteCashRegister,
  getUserActiveSession,
  getRegisterActiveSession,
  openCashRegisterSession,
  closeCashRegisterSession,
  getCashRegisterSessions,
  getCashRegisterSessionById,
  addPaymentTransaction,
  updatePaymentTransaction,
  deletePaymentTransaction,
  addCashWithdrawal,
  addCashDeposit,
  getLastSessionsForCashRegisters
} = require('../controllers/cash-register.controller');

// Kasa yönetimi
router.get('/cash-registers',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getAllCashRegisters);
router.get('/cash-registers/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getCashRegisterById);
router.post('/cash-registers',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addCashRegister);
router.put('/cash-registers/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  updateCashRegister);
router.delete('/cash-registers/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  deleteCashRegister);

// Kasa oturumları
router.get('/cash-register-sessions',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getCashRegisterSessions);
router.get('/cash-register-sessions/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getCashRegisterSessionById);
router.post('/cash-register-sessions/open',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  openCashRegisterSession);
router.post('/cash-register-sessions/:id/close',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  closeCashRegisterSession);

// Kullanıcı ve kasa oturumları
router.get('/user/active-session',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getUserActiveSession);
router.get('/cash-registers/:registerId/active-session',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getRegisterActiveSession);

// Ödeme işlemleri
router.post('/payment-transactions',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addPaymentTransaction);
router.put('/payment-transactions/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  updatePaymentTransaction);
router.delete('/payment-transactions/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  deletePaymentTransaction);

// Para çıkışı işlemleri
router.post('/cash-withdrawals',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addCashWithdrawal);

// Para girişi işlemleri
router.post('/cash-deposits',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addCashDeposit);

// Her kasa için son oturumları getir
router.get('/last-sessions',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getLastSessionsForCashRegisters);

module.exports = router;
