const { doCustomerExistDB, addCustomerDB, getCustomersDB, updateCustomerDB, deleteCustomerDB, getCustomerDB, searchCustomerDB } = require("../services/customer.service");

exports.addCustomer = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { phone, name, email, birthDate, gender, isMember, address } = req.body;

        if (!(phone && name)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: Name, Phone!"
            });
        }

        const doCustomerExist = await doCustomerExistDB(phone, tenantId);

        if (doCustomerExist) {
            return res.status(400).json({
                success: false,
                message: `${phone} Telefon numarası ile müşteri mevcut!`
            });
        }

        await addCustomerDB(phone, name, email, birthDate, gender, isMember, address, tenantId);

        return res.status(200).json({
            success: true,
            message: `${phone} Telefon numaralı müşteri eklendi.`
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Bir<PERSON>eyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};


exports.getCustomers = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const { page, perPage, sort, filter } = req.query;

        const result = await getCustomersDB(page, perPage, sort, filter, tenantId);

        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateCustomer = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const phone = req.params.id;
        const { name, email, birthDate, gender, address } = req.body;

        if (!(phone && name)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: Name, Phone!"
            });
        }

        await updateCustomerDB(phone, name, email, birthDate, gender, address, tenantId);

        return res.status(200).json({
            success: true,
            message: `${phone} telefon numaralı müşteri bilgileri güncellendi.`
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};



exports.deleteCustomer = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const phone = req.params.id;

        await deleteCustomerDB(phone, tenantId);

        return res.status(200).json({
            success: true,
            message: `${phone} numaralı müşteri silindi.`
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getCustomer = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const phone = req.params.id;

        const result = await getCustomerDB(phone, tenantId);

        if(result) {
            return res.status(200).json(result);
        }
        return res.status(404).json({
            success: false,
            message: `No Customer found with Phone: ${phone}`
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.searchCustomer = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const searchString = req.query.q;

        const result = await searchCustomerDB(searchString, tenantId);

        if(result.length > 0) {
            return res.status(200).json(result);
        }
        return res.status(404).json({
            success: false,
            message: `No Customers found!`
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};