const { getMySqlPromiseConnection } = require("../config/mysql.db")

const axios = require("axios");
const fs = require("fs");
const path = require("path");
const sharp = require("sharp"); // Resmi WEBP formatına dönüştürmek için


exports.addMenuItemDB = async (title, description, price, netPrice, taxId, categoryId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_items
        (title, description, price, net_price, tax_id, category, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [title, description, price, netPrice, taxId, categoryId, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.updateMenuItemDB = async (id, title, description, price, netPrice, taxId, categoryId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_items SET
        title = ?, description = ?, price = ?, net_price = ?, tax_id = ?, category = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [title, description, price, netPrice, taxId, categoryId, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.updateMenuItemImageDB = async (id, image, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_items SET
        image = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [image, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.deleteMenuItemDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_items
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.changeMenuItemVisibilityDB = async (id, isEnabled, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
         UPDATE menu_items SET
         is_enabled = ?
         WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [isEnabled, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.getAllMenuItemsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
        i.id, i.title, i.description, price, net_price, tax_id, t.title AS tax_title, t.rate AS tax_rate, t.type AS tax_type, category as category_id, c.title AS category_title, image, i.is_enabled, i.sort_order
        FROM menu_items i
        LEFT JOIN taxes t
        ON i.tax_id = t.id
        LEFT JOIN categories c
        ON i.category = c.id
        WHERE i.tenant_id = ? AND i.is_enabled = 1
        ORDER BY i.sort_order ASC, i.id ASC;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}


exports.getMenuItemDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
        i.id, i.title, i.description, price, net_price, tax_id, t.title AS tax_title, t.rate AS tax_rate, t.type AS tax_type, category as category_id, c.title AS category_title, image, i.is_enabled, i.sort_order
        FROM menu_items i
        LEFT JOIN taxes t
        ON i.tax_id = t.id
        LEFT JOIN categories c
        ON i.category = c.id
        WHERE i.id = ? AND i.tenant_id = ?
        `;

        const [result] = await conn.query(sql, [id, tenantId]);
        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}


/**
 * @param {number} itemId Menu Item ID to add Addon
 * @param {string} title Title of Addon
 * @param {number} price Additonal Price for addon, Put 0 / null to make addon as free option
 * @returns {Promise<number>}
 *  */
exports.addMenuItemAddonDB = async (itemId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_item_addons
        (item_id, title, price, tenant_id)
        VALUES
        (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [itemId, title, price, tenantId]);
        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemAddonDB = async (itemId, addonId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_item_addons
        SET
        title = ?, price = ?
        WHERE id = ? AND item_id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [title, price, addonId, itemId, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteMenuItemAddonDB = async (itemId, addonId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_item_addons
        WHERE id = ? AND item_id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [addonId, itemId, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getMenuItemAddonsDB = async (itemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_addons
        WHERE item_id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [itemId, tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllAddonsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_addons
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addMenuItemVariantDB = async (itemId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_item_variants
        (item_id, title, price, tenant_id)
        VALUES
        (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [itemId, title, price, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemVariantDB = async (itemId, variantId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_item_variants
        SET
        title = ?, price = ?
        WHERE item_id = ? AND id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [title, price, itemId, variantId, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteMenuItemVariantDB = async (itemId, variantId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_item_variants
        WHERE item_id = ? AND id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [itemId, variantId, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getMenuItemVariantsDB = async (itemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_variants
        WHERE item_id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [itemId, tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
exports.getAllVariantsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_variants
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemBulkDB = async (updateData, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const updatePromises = [];
        const updatedItems = {};

        // Her bir menü öğesi için güncelleme işlemi oluştur
        for (const [itemId, itemData] of Object.entries(updateData)) {
            // Güncellenecek alanları ve değerlerini hazırla
            const updateFields = [];
            const updateValues = [];

            for (const [field, value] of Object.entries(itemData)) {
                // Alan adını veritabanı formatına dönüştür (örn: category_id -> category)
                let dbField = field;
                if (field === 'category_id') dbField = 'category';
                if (field === 'tax_id') dbField = 'tax_id';

                updateFields.push(`${dbField} = ?`);
                updateValues.push(value);
            }

            // Sorgu parametrelerini tamamla
            updateValues.push(itemId, tenantId);

            // SQL sorgusunu oluştur
            const sql = `
                UPDATE menu_items
                SET ${updateFields.join(', ')}
                WHERE id = ? AND tenant_id = ?
            `;

            // Güncelleme işlemini promises dizisine ekle
            updatePromises.push(
                conn.query(sql, updateValues)
                    .then(() => {
                        updatedItems[itemId] = itemData;
                        return { itemId, success: true };
                    })
                    .catch(err => {
                        console.error(`Error updating item ${itemId}:`, err);
                        return { itemId, success: false, error: err.message };
                    })
            );
        }

        // Tüm güncelleme işlemlerini paralel olarak çalıştır
        await Promise.all(updatePromises);

        return updatedItems;
    } catch (error) {
        console.error("Error in updateMenuItemBulkDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.addRecipeItemDB = async (menuItemId, variantId, addonId, ingredientId, quantity, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        INSERT INTO menu_item_recipes
        (menu_item_id, variant_id, addon_id, inventory_item_id, quantity, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [menuItemId, variantId, addonId, ingredientId, quantity, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateRecipeItemDB = async (recipeItemId, menuItemId, variantId, addonId, ingredientId, quantity, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        UPDATE menu_item_recipes
        SET
          menu_item_id = ?,
          variant_id = ?,
          addon_id = ?,
          inventory_item_id = ?,
          quantity = ?
        WHERE id = ? AND tenant_id = ?;
      `;

        const [result] = await conn.query(sql, [
            menuItemId,
            variantId,
            addonId,
            ingredientId,
            quantity,
            recipeItemId,
            tenantId
        ]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getRecipeItemsDB = async (menuItemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        SELECT
            mir.id,
            mir.menu_item_id,
            mir.variant_id,
            mir.addon_id,
            mir.inventory_item_id,
            mi.title AS menu_item_title,
            v.title AS variant_title,
            a.title AS addon_title,
            ii.title AS ingredient_title,
            ii.unit,
            mir.quantity
        FROM
            menu_item_recipes mir
        LEFT JOIN
            menu_items mi ON mir.menu_item_id = mi.id
        LEFT JOIN
            menu_item_variants v ON mir.variant_id = v.id
        LEFT JOIN
            menu_item_addons a ON mir.addon_id = a.id
        LEFT JOIN
            inventory_items ii ON mir.inventory_item_id = ii.id
        WHERE
            mir.menu_item_id = ? AND mir.tenant_id = ?
        `;

        const [rows] = await conn.query(sql, [menuItemId, tenantId]);
        return rows;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllRecipeItemsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        SELECT
            mir.id,
            mir.menu_item_id,
            mir.variant_id,
            mir.addon_id,
            mir.inventory_item_id,
            mi.title AS menu_item_title,
            v.title AS variant_title,
            a.title AS addon_title,
            ii.title AS ingredient_title,
            ii.unit,
            ii.quantity as current_quantity,
            ii.min_quantity_threshold,
            mir.quantity as recipe_quantity
        FROM
            menu_item_recipes mir
        LEFT JOIN
            menu_items mi ON mir.menu_item_id = mi.id
        LEFT JOIN
            menu_item_variants v ON mir.variant_id = v.id
        LEFT JOIN
            menu_item_addons a ON mir.addon_id = a.id
        LEFT JOIN
            inventory_items ii ON mir.inventory_item_id = ii.id
        WHERE
            mir.tenant_id = ?
        `;

        const [rows] = await conn.query(sql, [tenantId]);
        return rows;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteRecipeItemDB = async (itemId, recipeItemId, variant, addon, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        let sql = `
        DELETE FROM menu_item_recipes
        WHERE menu_item_id = ?
        AND id = ?
        AND tenant_id = ?
      `;

        const params = [itemId, recipeItemId, tenantId];

        if (variant) {
            sql += ` AND variant_id = ?`;
            params.push(variant);
        }

        if (addon) {
            sql += ` AND addon_id = ?`;
            params.push(addon);
        }

        await conn.query(sql, params);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getOrCreateCategoryId = async (categoryTitle, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Kategoriyi kontrol et
        const sqlCheck = `
        SELECT id FROM categories
        WHERE title = ? AND tenant_id = ?
        LIMIT 1
        `;
        const [rows] = await conn.query(sqlCheck, [categoryTitle, tenantId]);

        // Eğer kategori mevcutsa ID'yi döndür
        if (rows.length > 0) {
            return rows[0].id;
        }

        // Kategori mevcut değilse yeni bir kategori oluştur
        const sqlInsert = `
        INSERT INTO categories (title, tenant_id)
        VALUES (?, ?)
        `;
        const [result] = await conn.query(sqlInsert, [categoryTitle, tenantId]);

        // Yeni oluşturulan kategori ID'sini döndür
        return result.insertId;
    } catch (error) {
        console.error("Error in getOrCreateCategoryId:", error);
        throw error;
    } finally {
        conn.release();
    }
};
exports.addMenuBulkDB = async (menuItems, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const bulkInsertValues = [];
        for (const item of menuItems) {
            const {
                title,
                description,
                price,
                netPrice,
                taxId,
                categoryTitle,
                stock,
                isStockActive,
            } = item;

            // Kategori başlığını ID'ye çevir (gerekirse oluştur)
            const categoryId = await exports.getOrCreateCategoryId(categoryTitle, tenantId);

            // Değerleri toplu eklemek için hazırlayın
            bulkInsertValues.push([
                title,
                description || null,
                price,
                netPrice || null,
                taxId || null,
                categoryId,
                stock || 0,
                isStockActive || 0,
                tenantId,
            ]);
        }

        // Toplu ekleme işlemi
        const sql = `
        INSERT INTO menu_items
        (title, description, price, net_price, tax_id, category, stock, is_stock_active, tenant_id)
        VALUES ?
        `;
        const [result] = await conn.query(sql, [bulkInsertValues]);

        return result;
    } catch (error) {
        console.error("Error in addMenuBulkDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.downloadAndUpdateImage = async (imageUrl, tenantId, menuItemId) => {
    try {
        const imageDir = path.join(__dirname, `../../public/${tenantId}/`);
        const imageName = `${menuItemId}.webp`; // Resim adı WEBP formatında
        const imageFilePath = path.join(imageDir, imageName);

        // Klasör yoksa oluştur
        if (!fs.existsSync(imageDir)) {
            fs.mkdirSync(imageDir, { recursive: true });
        }

        // Resmi indir
        const response = await axios({
            url: imageUrl,
            method: "GET",
            responseType: "arraybuffer",
        });

        // Resmi indir ve WEBP formatına dönüştür
        await sharp(response.data)
            .webp({ quality: 80 }) // WEBP formatına dönüştürme, kalite %80
            .toFile(imageFilePath);

        return `/public/${tenantId}/${imageName}`; // WEBP formatında yeni resim yolu
    } catch (error) {
        console.error(`Resim indirilemedi veya dönüştürülemedi (${imageUrl}):`, error);
        return null; // Hata durumunda null döndür
    }
};

exports.updateMenuItemsBulkDB = async (menuItems, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const updatePromises = menuItems.map(async (item) => {
            const {
                id,
                title,
                description,
                price,
                netPrice,
                categoryId,
                taxID,
                stock,
                isStockActive,
                imageUrl,
            } = item;

            try {

                // Resim işlemleri
                let imagePath = null;
                if (imageUrl) {
                    imagePath = await exports.downloadAndUpdateImage(imageUrl, tenantId, id); // Resmi indir ve güncelle
                }

                // Güncelleme SQL
                const sql = `
                UPDATE menu_items SET
                title = ?, description = ?, price = ?, net_price = ?, tax_id = ?, category = ?,
                stock = ?, is_stock_active = ?, image = COALESCE(?, image)
                WHERE id = ? AND tenant_id = ?
                `;
                return conn.query(sql, [
                    title,
                    description || null,
                    price,
                    netPrice || null,
                    taxID || null,
                    categoryId,
                    stock || 0,
                    isStockActive || 0,
                    imagePath, // Eğer yeni bir resim URL'si yoksa mevcut resmi koruyacak
                    id,
                    tenantId,
                ]);
            } catch (innerError) {
                console.error(`Ürün güncellenirken hata oluştu (ID: ${item.id}):`, innerError);
                return null; // Hata durumunda bu öğeyi atla
            }
        });

        await Promise.all(updatePromises);

        return { success: true };
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateCategoryOrderDB = async (categoryOrder, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      // Kategorilerin sıralamasını toplu olarak güncelle
      const updatePromises = categoryOrder.map((category) => {
        const sql = `
          UPDATE categories
          SET sort_order = ?
          WHERE id = ? AND tenant_id = ?;
        `;
        return conn.query(sql, [category.order, category.id, tenantId]);
      });

      await Promise.all(updatePromises);

      return { success: true, message: "Kategori sıralaması başarıyla güncellendi." };
    } catch (error) {
      console.error("Error in updateCategoryOrderDB:", error);
      throw error;
    } finally {
      conn.release();
    }
  };

exports.updateMenuItemOrderDB = async (menuItemOrder, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      // Menü öğelerinin sıralamasını toplu olarak güncelle
      const updatePromises = menuItemOrder.map((menuItem) => {
        const sql = `
          UPDATE menu_items
          SET sort_order = ?
          WHERE id = ? AND tenant_id = ?;
        `;
        return conn.query(sql, [menuItem.order, menuItem.id, tenantId]);
      });

      await Promise.all(updatePromises);

      return { success: true, message: "Menü öğeleri sıralaması başarıyla güncellendi." };
    } catch (error) {
      console.error("Error in updateMenuItemOrderDB:", error);
      throw error;
    } finally {
      conn.release();
    }
  };