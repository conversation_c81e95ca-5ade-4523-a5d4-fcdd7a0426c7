const {
  getStorageLocationSettingsDB,
  updateStorageLocationSettingsDB,
  getStorageLocationsDB,
  getStorageLocationByIdDB,
  createStorageLocationDB,
  updateStorageLocationDB,
  deleteStorageLocationDB,
  getStorageLocationFloorsDB,
  createStorageLocationFloorMappingDB,
  updateStorageLocationFloorMappingDB,
  deleteStorageLocationFloorMappingDB,
  getLocationInventoryDB,
  addLocationStockMovementDB,
  transferStockBetweenLocationsDB,
  getLocationStockMovementsDB,
  getLocationStockSummaryDB
} = require("../services/storage-location.service");

/**
 * Depo/Bar Lokasyonları Modülü Controller
 * Bu modül işletmelerin farklı depo ve barlarını yönetmesini sağlar
 */

// ============================================================================
// MODÜL AYARLARI
// ============================================================================

/**
 * Modül ayarlarını getirir
 */
exports.getStorageLocationSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const settings = await getStorageLocationSettingsDB(tenantId);

    return res.status(200).json(settings);
  } catch (error) {
    console.error("getStorageLocationSettings error:", error);
    return res.status(500).json({
      success: false,
      message: "Modül ayarları getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Modül ayarlarını günceller
 */
exports.updateStorageLocationSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const settings = req.body;

    const success = await updateStorageLocationSettingsDB(tenantId, settings);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "Ayarlar güncellenemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Modül ayarları başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updateStorageLocationSettings error:", error);
    return res.status(500).json({
      success: false,
      message: "Modül ayarları güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

// ============================================================================
// LOKASYON YÖNETİMİ
// ============================================================================

/**
 * Tüm depo/bar lokasyonlarını getirir
 */
exports.getStorageLocations = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const filters = {
      type: req.query.type,
      is_active: req.query.is_active !== undefined ? req.query.is_active === 'true' : undefined
    };

    const locations = await getStorageLocationsDB(tenantId, filters);

    return res.status(200).json(locations);
  } catch (error) {
    console.error("getStorageLocations error:", error);
    return res.status(500).json({
      success: false,
      message: "Lokasyonlar getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Belirli bir lokasyonu getirir
 */
exports.getStorageLocationById = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const location = await getStorageLocationByIdDB(id, tenantId);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: "Lokasyon bulunamadı"
      });
    }

    return res.status(200).json(location);
  } catch (error) {
    console.error("getStorageLocationById error:", error);
    return res.status(500).json({
      success: false,
      message: "Lokasyon getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Yeni depo/bar lokasyonu oluşturur
 */
exports.createStorageLocation = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const locationData = req.body;

    // Gerekli alanları kontrol et
    if (!locationData.name || !locationData.type) {
      return res.status(400).json({
        success: false,
        message: "Lokasyon adı ve türü gereklidir"
      });
    }

    const locationId = await createStorageLocationDB(locationData, tenantId);

    return res.status(201).json({
      success: true,
      message: "Lokasyon başarıyla oluşturuldu",
      data: { id: locationId }
    });
  } catch (error) {
    console.error("createStorageLocation error:", error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: "Bu lokasyon kodu zaten kullanılıyor"
      });
    }

    return res.status(500).json({
      success: false,
      message: "Lokasyon oluşturulurken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Depo/bar lokasyonunu günceller
 */
exports.updateStorageLocation = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const locationData = req.body;

    const success = await updateStorageLocationDB(id, locationData, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Lokasyon bulunamadı veya güncellenemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Lokasyon başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updateStorageLocation error:", error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: "Bu lokasyon kodu zaten kullanılıyor"
      });
    }

    return res.status(500).json({
      success: false,
      message: "Lokasyon güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Depo/bar lokasyonunu siler
 */
exports.deleteStorageLocation = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const success = await deleteStorageLocationDB(id, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Lokasyon bulunamadı"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Lokasyon başarıyla silindi"
    });
  } catch (error) {
    console.error("deleteStorageLocation error:", error);
    
    const knownErrors = [
      "Lokasyon bulunamadı",
      "Varsayılan lokasyon silinemez",
      "Bu lokasyonda stok hareketleri bulunduğu için silinemez"
    ];

    if (knownErrors.includes(error.message)) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: "Lokasyon silinirken bir hata oluştu",
      error: error.message
    });
  }
};

// ============================================================================
// FLOOR-LOKASYON EŞLEŞTİRME
// ============================================================================

/**
 * Floor-lokasyon eşleştirmelerini getirir
 */
exports.getStorageLocationFloors = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { floor_id, location_id } = req.query;

    const mappings = await getStorageLocationFloorsDB(
      tenantId,
      floor_id ? parseInt(floor_id) : null,
      location_id ? parseInt(location_id) : null
    );

    return res.status(200).json(mappings);
  } catch (error) {
    console.error("getStorageLocationFloors error:", error);
    return res.status(500).json({
      success: false,
      message: "Floor-lokasyon eşleştirmeleri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Floor-lokasyon eşleştirmesi oluşturur
 */
exports.createStorageLocationFloorMapping = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const mappingData = req.body;

    // Gerekli alanları kontrol et
    if (!mappingData.storage_location_id || !mappingData.floor_id) {
      return res.status(400).json({
        success: false,
        message: "Lokasyon ID ve Floor ID gereklidir"
      });
    }

    const mappingId = await createStorageLocationFloorMappingDB(mappingData, tenantId);

    return res.status(201).json({
      success: true,
      message: "Floor-lokasyon eşleştirmesi başarıyla oluşturuldu",
      data: { id: mappingId }
    });
  } catch (error) {
    console.error("createStorageLocationFloorMapping error:", error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: "Bu floor-lokasyon eşleştirmesi zaten mevcut"
      });
    }

    return res.status(500).json({
      success: false,
      message: "Floor-lokasyon eşleştirmesi oluşturulurken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Floor-lokasyon eşleştirmesini günceller
 */
exports.updateStorageLocationFloorMapping = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const mappingData = req.body;

    const success = await updateStorageLocationFloorMappingDB(id, mappingData, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Floor-lokasyon eşleştirmesi bulunamadı"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Floor-lokasyon eşleştirmesi başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updateStorageLocationFloorMapping error:", error);
    return res.status(500).json({
      success: false,
      message: "Floor-lokasyon eşleştirmesi güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Floor-lokasyon eşleştirmesini siler
 */
exports.deleteStorageLocationFloorMapping = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const success = await deleteStorageLocationFloorMappingDB(id, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Floor-lokasyon eşleştirmesi bulunamadı"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Floor-lokasyon eşleştirmesi başarıyla silindi"
    });
  } catch (error) {
    console.error("deleteStorageLocationFloorMapping error:", error);
    return res.status(500).json({
      success: false,
      message: "Floor-lokasyon eşleştirmesi silinirken bir hata oluştu",
      error: error.message
    });
  }
};

// ============================================================================
// LOKASYON BAZLI STOK YÖNETİMİ
// ============================================================================

/**
 * Lokasyon bazlı stok miktarlarını getirir
 */
exports.getLocationInventory = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const filters = {
      low_stock: req.query.low_stock === 'true',
      out_of_stock: req.query.out_of_stock === 'true',
      inventory_item_id: req.query.inventory_item_id ? parseInt(req.query.inventory_item_id) : null
    };

    const inventory = await getLocationInventoryDB(id, tenantId, filters);

    return res.status(200).json(inventory);
  } catch (error) {
    console.error("getLocationInventory error:", error);
    return res.status(500).json({
      success: false,
      message: "Lokasyon stok bilgileri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Lokasyon bazlı stok hareketi yapar
 */
exports.addLocationStockMovement = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const { id } = req.params;
    const movementData = { ...req.body, storage_location_id: id };

    // Gerekli alanları kontrol et
    if (!movementData.inventory_item_id || !movementData.movement_type || !movementData.quantity) {
      return res.status(400).json({
        success: false,
        message: "Stok kalemi ID, hareket türü ve miktar gereklidir"
      });
    }

    const success = await addLocationStockMovementDB(movementData, tenantId, username);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "Stok hareketi gerçekleştirilemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Stok hareketi başarıyla gerçekleştirildi"
    });
  } catch (error) {
    console.error("addLocationStockMovement error:", error);

    if (error.message.includes("Yetersiz stok")) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: "Stok hareketi gerçekleştirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Lokasyonlar arası stok transferi yapar
 */
exports.transferStockBetweenLocations = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const transferData = req.body;

    // Gerekli alanları kontrol et
    const requiredFields = ['from_location_id', 'to_location_id', 'inventory_item_id', 'quantity'];
    for (const field of requiredFields) {
      if (!transferData[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} gereklidir`
        });
      }
    }

    if (transferData.from_location_id === transferData.to_location_id) {
      return res.status(400).json({
        success: false,
        message: "Kaynak ve hedef lokasyon aynı olamaz"
      });
    }

    const success = await transferStockBetweenLocationsDB(transferData, tenantId, username);

    if (!success) {
      return res.status(400).json({
        success: false,
        message: "Stok transferi gerçekleştirilemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Stok transferi başarıyla gerçekleştirildi"
    });
  } catch (error) {
    console.error("transferStockBetweenLocations error:", error);

    if (error.message.includes("Yetersiz stok")) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: "Stok transferi gerçekleştirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Lokasyon bazlı stok hareketlerini getirir
 */
exports.getLocationStockMovements = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const filters = {
      storage_location_id: req.query.storage_location_id ? parseInt(req.query.storage_location_id) : null,
      inventory_item_id: req.query.inventory_item_id ? parseInt(req.query.inventory_item_id) : null,
      movement_type: req.query.movement_type,
      from_date: req.query.from_date,
      to_date: req.query.to_date,
      limit: req.query.limit ? parseInt(req.query.limit) : 100
    };

    const movements = await getLocationStockMovementsDB(tenantId, filters);

    return res.status(200).json(movements);
  } catch (error) {
    console.error("getLocationStockMovements error:", error);
    return res.status(500).json({
      success: false,
      message: "Stok hareketleri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Lokasyon bazlı stok özet raporu
 */
exports.getLocationStockSummary = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const summary = await getLocationStockSummaryDB(tenantId);

    return res.status(200).json(summary);
  } catch (error) {
    console.error("getLocationStockSummary error:", error);
    return res.status(500).json({
      success: false,
      message: "Stok özet raporu getirilirken bir hata oluştu",
      error: error.message
    });
  }
};
