const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  addMapping,
  getMappings,
  updateMapping,
  deleteMapping
} = require("../controllers/floorPrinterMappings.controller");

const router = Router();

router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]),
  getMappings
);

router.post(
  "/add",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]),
  addMapping
);

router.put(
  "/update/:mappingId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]),
  updateMapping
);

router.delete(
  "/delete/:mappingId",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]),
  deleteMapping
);

module.exports = router;
