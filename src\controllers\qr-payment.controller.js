const { getOrdersPaymentSummaryDB, savePartialPaymentDB, createInvoiceDB, completeOrdersAndSaveInvoiceIdDB } = require('../services/orders.service');
const { decrypt } = require('../config/crypto');
const { addPaymentTransactionDB } = require('../services/cash-register.service');

/**
 * QR kod ile ödeme işlemini gerçekleştirir
 */
exports.processQRPayment = async (req, res) => {
  try {
    console.log("Tüm istek:", req.body);

    const tenantId = 3;

    let amount = req.body.amount;
    let qrCodeContent = req.body.qrcodeconent;

    if (amount === undefined) amount = req.body.Amount;
    if (qrCodeContent === undefined) qrCodeContent = req.body.qrCodeContent;
    if (qrCodeContent === undefined) qrCodeContent = req.body.qrcode;
    if (qrCodeContent === undefined) qrCodeContent = req.body.qrContent;
    if (qrCodeContent === undefined) qrCodeContent = req.body.QRCodeContent;

    if (!amount || !qrCodeContent) {
      return res.status(400).json({
        success: false,
        message: "Ödeme tutarı ve QR kod içeriği gereklidir",
        receivedBody: req.body
      });
    }

    const orderId = decrypt(qrCodeContent);

    const orderSummary = await getOrdersPaymentSummaryDB([orderId], tenantId);

    if (!orderSummary) {
      return res.status(404).json({
        success: false,
        message: "Sipariş bulunamadı"
      });
    }

    const orderTotal = orderSummary.total;

    if (parseFloat(amount) === parseFloat(orderTotal)) {
      console.log("Tam ödeme yapılıyor...");

      const now = new Date();
      const options = { timeZone: 'Europe/Istanbul' };
      const turkeyNow = new Date(now.toLocaleString('en-US', options));

      const date = `${turkeyNow.getFullYear()}-${(turkeyNow.getMonth() + 1).toString().padStart(2, '0')}-${turkeyNow.getDate().toString().padStart(2, '0')} ${turkeyNow.getHours().toString().padStart(2, '0')}:${turkeyNow.getMinutes().toString().padStart(2, '0')}:${turkeyNow.getSeconds().toString().padStart(2, '0')}`;

      const invoiceId = await createInvoiceDB(
        orderSummary.subtotal,
        orderSummary.taxTotal,
        orderSummary.total,
        date,
        5, // ödeme tipi
        tenantId
      );

      await completeOrdersAndSaveInvoiceIdDB([orderId], invoiceId, tenantId);

      // Ödeme işlemini payment_transactions tablosuna kaydet
      try {
        await addPaymentTransactionDB({
          order_id: orderId,
          payment_type_id: 5, // QR ödeme tipi
          amount: parseFloat(amount),
          transaction_type: 'payment',
          notes: 'QR Ödeme',
          created_by: '<EMAIL>', // QR ödemeler sistem tarafından yapılır
          invoice_id: invoiceId,
          cash_register_session_id: null // QR ödemeler kasa oturumuna bağlı değildir
        }, tenantId);
      } catch (paymentError) {
        console.error("Ödeme kaydedilirken hata:", paymentError);
        // Ödeme kaydedilemese bile işleme devam et
      }

      // Socket bildirimi gönder
      if (global.io) {
        try {
          global.io.to(tenantId.toString()).emit('order-payment-completed', {
            orderId: orderId,
            paymentType: 5, // QR ödeme tipi
            amount: parseFloat(amount),
            invoiceId: invoiceId,
            date: date,
            isFullPayment: true
          });
        } catch (socketError) {
          console.error('Socket gönderim hatası:', socketError);
        }
      }

      return res.status(200).json({
        success: true,
        message: "Ödeme başarıyla tamamlandı"
      });
    } else {
      console.log("Kısmi ödeme yapılıyor...");

      const payments = [{
        method: 8,
        amount: parseFloat(amount),
        orderItem_id: null,
        productName: "Genel Ödeme",
        order_id: orderId
      }];

      await savePartialPaymentDB(payments, tenantId, 'system');

      // Ödeme işlemini payment_transactions tablosuna kaydet
      try {
        await addPaymentTransactionDB({
          order_id: orderId,
          payment_type_id: 8, // QR kısmi ödeme tipi
          amount: parseFloat(amount),
          transaction_type: 'payment',
          notes: 'QR Kısmi Ödeme',
          created_by: '<EMAIL>', // QR ödemeler sistem tarafından yapılır
          invoice_id: null,
          cash_register_session_id: null // QR ödemeler kasa oturumuna bağlı değildir
        }, tenantId);
      } catch (paymentError) {
        console.error("Ödeme kaydedilirken hata:", paymentError);
        // Ödeme kaydedilemese bile işleme devam et
      }

      // Socket bildirimi gönder
      if (global.io) {
        try {
          global.io.to(tenantId.toString()).emit('partial-payment-added', {
            orderIds: [orderId],
            paymentType: 8, // QR kısmi ödeme tipi
            amount: parseFloat(amount),
            date: new Date().toISOString(),
            isFullPayment: false
          });
        } catch (socketError) {
          console.error('Socket gönderim hatası:', socketError);
        }
      }

      return res.status(200).json({
        success: true,
        message: "Kısmi ödeme başarıyla kaydedildi"
      });
    }
  } catch (error) {
    console.error("QR ödeme işlemi sırasında hata:", error);

    return res.status(500).json({
      success: false,
      message: "Ödeme işlemi sırasında bir hata oluştu",
      error: error.message
    });
  }
};
