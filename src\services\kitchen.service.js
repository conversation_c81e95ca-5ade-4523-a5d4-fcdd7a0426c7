const { getMySqlPromiseConnection } = require("../config/mysql.db")

exports.getKitchenOrdersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {

    const sql = `
    SELECT
      o.id,
      o.date,
      o.delivery_type,
      o.customer_type,
      o.customer_id,
      c. \`name\` AS customer_name,
      o.table_id,
      st.table_title,
      st. \`floor\`,
      o.status,
      o.payment_status,
      o.token_no
    FROM
      orders o
      LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
      LEFT JOIN store_tables st ON o.table_id = st.id
    WHERE
      date >= DATE_SUB(NOW(), INTERVAL 1 DAY)
      AND date <= DATE_ADD(NOW(), INTERVAL 1 DAY)
      AND o.status NOT IN ('completed', 'cancelled')
      AND o.tenant_id = ?
    `;

    const [kitchenOrders] = await conn.query(sql, [tenantId]);

    let kitchenOrdersItems = [];
    let addons = [];

    if(kitchenOrders.length > 0) {
      const orderIds = kitchenOrders.map(o=>o.id).join(",");
      const sql2 = `
      SELECT
        oi.id,
        oi.order_id,
        oi.item_id,
        mi.title AS item_title,
        oi.variant_id,
        miv.title as variant_title,
        oi.quantity,
        oi.status,
        oi.date,
        oi.addons,
        oi.notes
      FROM
        order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT join menu_item_variants miv ON oi.item_id = miv.item_id AND oi.variant_id = miv.id

      WHERE oi.order_id IN (${orderIds})
      `
      const [kitchenOrdersItemsResult] = await conn.query(sql2);
      kitchenOrdersItems = kitchenOrdersItemsResult;

      const addonIds = [...new Set([...kitchenOrdersItems.flatMap((o)=>o.addons?JSON.parse(o?.addons):[])])].join(",");
      const [addonsResult] = addonIds ? await conn.query(`SELECT id, item_id, title FROM menu_item_addons WHERE id IN (${addonIds});`):[]
      addons = addonsResult;
    }

    return {
      kitchenOrders,
      kitchenOrdersItems,
      addons
    }

  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateOrderItemStatusDB = async (orderItemId, status, tenantId, username, reasonId = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let sql;
    let params;

    if (status === 'cancelled') {
      sql = `
      UPDATE order_items SET
      status = ?,
      cancelled_by = ?,
      cancelled_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else if (status === 'complimentary') {
      sql = `
      UPDATE order_items SET
      status = ?,
      complimentary_by = ?,
      complimentary_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else if (status === 'waste') {
      sql = `
      UPDATE order_items SET
      status = ?,
      waste_by = ?,
      waste_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else {
      sql = `
      UPDATE order_items SET
      status = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, orderItemId, tenantId];
    }

    await conn.query(sql, params);
    return;
} catch (error) {
    console.error(error);
    throw error;
} finally {
  conn.release();
}
};