const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * Tüm bankaları getirir
 * @param {number} tenantId - Kiracı ID
 * @param {boolean} activeOnly - Sadece aktif bankaları getir
 * @returns {Array} Bankalar listesi
 */
exports.getAllBanksDB = async (tenantId, activeOnly = false) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let sql = `
      SELECT
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM
        banks
      WHERE
        tenant_id = ?
    `;

    const params = [tenantId];

    if (activeOnly) {
      sql += " AND is_active = 1";
    }

    sql += " ORDER BY name ASC";

    const [banks] = await conn.query(sql, params);
    return banks;
  } catch (error) {
    console.error("getAllBanksDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * ID'ye göre banka getirir
 * @param {number} bankId - Banka ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Banka bilgileri
 */
exports.getBankByIdDB = async (bankId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM
        banks
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [banks] = await conn.query(sql, [bankId, tenantId]);
    return banks.length > 0 ? banks[0] : null;
  } catch (error) {
    console.error("getBankByIdDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Yeni banka ekler
 * @param {Object} bankData - Banka bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen bankanın ID'si
 */
exports.addBankDB = async (bankData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { name, description } = bankData;

    const sql = `
      INSERT INTO banks
        (name, description, is_active, tenant_id)
      VALUES
        (?, ?, 1, ?)
    `;

    const [result] = await conn.query(sql, [name, description, tenantId]);
    return result.insertId;
  } catch (error) {
    console.error("addBankDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Banka bilgilerini günceller
 * @param {number} bankId - Banka ID
 * @param {Object} bankData - Güncellenecek banka bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} Güncelleme başarılı mı
 */
exports.updateBankDB = async (bankId, bankData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { name, description, is_active } = bankData;

    const sql = `
      UPDATE banks
      SET
        name = ?,
        description = ?,
        is_active = ?,
        updated_at = NOW()
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [name, description, is_active, bankId, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("updateBankDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Banka siler
 * @param {number} bankId - Banka ID
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} Silme başarılı mı
 */
exports.deleteBankDB = async (bankId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce bu bankanın kasa kapatma detaylarında kullanılıp kullanılmadığını kontrol et
    const [usageCheck] = await conn.query(
      "SELECT COUNT(*) as count FROM cash_register_session_bank_details WHERE bank_id = ? AND tenant_id = ?",
      [bankId, tenantId]
    );

    if (usageCheck[0].count > 0) {
      throw new Error("Bu banka kasa kapatma detaylarında kullanıldığı için silinemez. Pasif hale getirebilirsiniz.");
    }

    const sql = `
      DELETE FROM banks
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [bankId, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("deleteBankDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa kapatma banka detaylarını ekler
 * @param {number} sessionId - Kasa oturumu ID
 * @param {Array} bankDetails - Banka detayları [{bank_id, amount, notes}]
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} Ekleme başarılı mı
 */
exports.addCashRegisterSessionBankDetailsDB = async (sessionId, bankDetails, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    if (!bankDetails || bankDetails.length === 0) {
      return true; // Banka detayı yoksa başarılı kabul et
    }

    await conn.beginTransaction();

    // Önce mevcut detayları sil (güncelleme durumu için)
    await conn.query(
      "DELETE FROM cash_register_session_bank_details WHERE cash_register_session_id = ? AND tenant_id = ?",
      [sessionId, tenantId]
    );

    // Yeni detayları ekle
    const sql = `
      INSERT INTO cash_register_session_bank_details
        (cash_register_session_id, bank_id, amount, notes, tenant_id)
      VALUES
        (?, ?, ?, ?, ?)
    `;

    for (const detail of bankDetails) {
      const { bank_id, amount, notes } = detail;
      
      // Tutarı kontrol et
      if (amount && parseFloat(amount) > 0) {
        await conn.query(sql, [sessionId, bank_id, amount, notes || null, tenantId]);
      }
    }

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    console.error("addCashRegisterSessionBankDetailsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa oturumu banka detaylarını getirir
 * @param {number} sessionId - Kasa oturumu ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Banka detayları
 */
exports.getCashRegisterSessionBankDetailsDB = async (sessionId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        bd.id,
        bd.bank_id,
        b.name as bank_name,
        bd.amount,
        bd.notes,
        bd.created_at
      FROM
        cash_register_session_bank_details bd
      INNER JOIN
        banks b ON bd.bank_id = b.id
      WHERE
        bd.cash_register_session_id = ? AND bd.tenant_id = ?
      ORDER BY
        b.name ASC
    `;

    const [details] = await conn.query(sql, [sessionId, tenantId]);
    return details;
  } catch (error) {
    console.error("getCashRegisterSessionBankDetailsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
