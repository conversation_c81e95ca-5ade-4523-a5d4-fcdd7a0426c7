const express = require('express');
const router = express.Router();
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getAllBanks,
  getBankById,
  addBank,
  updateBank,
  deleteBank
} = require('../controllers/bank.controller');

// Banka yönetimi
router.get('/banks',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getAllBanks);

router.get('/banks/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getBankById);

router.post('/banks',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addBank);

router.put('/banks/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  updateBank);

router.delete('/banks/:id',
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  deleteBank);

module.exports = router;
