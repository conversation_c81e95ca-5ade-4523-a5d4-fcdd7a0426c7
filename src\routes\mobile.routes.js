// routes/mobile.routes.js
const { Router } = require("express");
const { 
    getTenantInfo,
    getAllTenants,
    getMenuItems,
    getCategories,
    getTables,
    updateTableStatus,
    createOrder,
    getOrders,
    updateOrderStatus
} = require("../controllers/mobile.controller");
const { authenticateToken } = require("../middlewares/auth.middleware");

const router = Router();

// Tenant Routes
router.get("/tenants/:tenantId", getTenantInfo);
router.get("/tenants", getAllTenants);




module.exports = router;