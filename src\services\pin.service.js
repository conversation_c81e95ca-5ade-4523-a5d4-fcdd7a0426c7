const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.setPinDB = async (userId, pin, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      UPDATE users 
      SET pin = ? 
      WHERE id = ? AND tenant_id = ?
    `;
    await conn.query(sql, [pin, userId, tenantId]);
    return true;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.verifyPinDB = async (pin, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT * FROM users 
      WHERE pin = ? AND tenant_id = ? 
      LIMIT 1
    `;
    const [users] = await conn.query(sql, [pin, tenantId]);
    return users[0] || null;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};