// controllers/floorPrinterMappings.controller.js
const {
    addFloorPrinterMappingService,
    getFloorPrinterMappingsService,
    updateFloorPrinterMappingService,
    deleteFloorPrinterMappingService
  } = require("../services/floorPrinterMappings.service");
  
  exports.addMapping = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const { floorId, categoryId, printerId, printerType } = req.body;
  
      if (!floorId || !printerId || !printerType) {
        return res.status(400).json({ success: false, message: "floorId, printerId ve printerType alanları zorunludur." });
      }
      if (!["kitchen", "receipt"].includes(printerType)) {
        return res.status(400).json({ success: false, message: "Geçersiz printerType değeri." });
      }
  
      await addFloorPrinterMappingService(tenantId, floorId, categoryId, printerId, printerType);
      return res.status(201).json({ success: true, message: "Mapping başarıyla eklendi." });
    } catch (error) {
      console.error("Mapping eklenirken hata:", error);
      return res.status(500).json({ success: false, message: "Mapping eklenemedi", error: error.message });
    }
  };
  
  exports.getMappings = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const mappings = await getFloorPrinterMappingsService(tenantId);
      return res.status(200).json(mappings);
    } catch (error) {
      console.error("Mapping'ler alınırken hata:", error);
      return res.status(500).json({ success: false, message: "Mapping'ler alınamadı", error: error.message });
    }
  };
  
  exports.updateMapping = async (req, res) => {
    try {
      const { mappingId } = req.params;
      const { floorId, categoryId, printerId, printerType } = req.body;
  
      if (!floorId || !printerId || !printerType) {
        return res.status(400).json({ success: false, message: "floorId, printerId ve printerType alanları zorunludur." });
      }
      if (!["kitchen", "receipt"].includes(printerType)) {
        return res.status(400).json({ success: false, message: "Geçersiz printerType değeri." });
      }
  
      await updateFloorPrinterMappingService(mappingId, floorId, categoryId, printerId, printerType);
      return res.status(200).json({ success: true, message: "Mapping başarıyla güncellendi." });
    } catch (error) {
      console.error("Mapping güncellenirken hata:", error);
      return res.status(500).json({ success: false, message: "Mapping güncellenemedi", error: error.message });
    }
  };
  
  exports.deleteMapping = async (req, res) => {
    try {
      const { mappingId } = req.params;
      await deleteFloorPrinterMappingService(mappingId);
      return res.status(200).json({ success: true, message: "Mapping başarıyla silindi." });
    } catch (error) {
      console.error("Mapping silinirken hata:", error);
      return res.status(500).json({ success: false, message: "Mapping silinemedi", error: error.message });
    }
  };